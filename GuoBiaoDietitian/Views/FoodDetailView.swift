import SwiftUI

struct FoodDetailView: View {
    let food: Food
    let mealType: FoodRecord.MealType
    let onAddFood: (FoodRecord) -> Void
    
    @Environment(\.dismiss) private var dismiss
    @State private var amount: Double = 100
    @State private var notes: String = ""
    @State private var showingNutritionDetails = false
    
    private var actualNutrition: NutritionFacts {
        let ratio = amount / 100.0
        return NutritionFacts(
            energy: food.nutritionFacts.energy * ratio,
            protein: food.nutritionFacts.protein * ratio,
            fat: food.nutritionFacts.fat * ratio,
            carbohydrate: food.nutritionFacts.carbohydrate * ratio,
            sodium: food.nutritionFacts.sodium * ratio,
            saturatedFat: food.nutritionFacts.saturatedFat.map { $0 * ratio },
            transFat: food.nutritionFacts.transFat.map { $0 * ratio },
            cholesterol: food.nutritionFacts.cholesterol.map { $0 * ratio },
            dietaryFiber: food.nutritionFacts.dietaryFiber.map { $0 * ratio },
            sugar: food.nutritionFacts.sugar.map { $0 * ratio },
            vitaminA: food.nutritionFacts.vitaminA.map { $0 * ratio },
            vitaminD: food.nutritionFacts.vitaminD.map { $0 * ratio },
            vitaminE: food.nutritionFacts.vitaminE.map { $0 * ratio },
            vitaminC: food.nutritionFacts.vitaminC.map { $0 * ratio },
            thiamine: food.nutritionFacts.thiamine.map { $0 * ratio },
            riboflavin: food.nutritionFacts.riboflavin.map { $0 * ratio },
            niacin: food.nutritionFacts.niacin.map { $0 * ratio },
            vitaminB6: food.nutritionFacts.vitaminB6.map { $0 * ratio },
            folate: food.nutritionFacts.folate.map { $0 * ratio },
            vitaminB12: food.nutritionFacts.vitaminB12.map { $0 * ratio },
            calcium: food.nutritionFacts.calcium.map { $0 * ratio },
            iron: food.nutritionFacts.iron.map { $0 * ratio },
            phosphorus: food.nutritionFacts.phosphorus.map { $0 * ratio },
            potassium: food.nutritionFacts.potassium.map { $0 * ratio },
            magnesium: food.nutritionFacts.magnesium.map { $0 * ratio },
            zinc: food.nutritionFacts.zinc.map { $0 * ratio },
            selenium: food.nutritionFacts.selenium.map { $0 * ratio },
            copper: food.nutritionFacts.copper.map { $0 * ratio },
            manganese: food.nutritionFacts.manganese.map { $0 * ratio },
            iodine: food.nutritionFacts.iodine.map { $0 * ratio }
        )
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 食物基本信息
                    FoodBasicInfoCard(food: food)
                    
                    // 分量设置
                    AmountSettingCard(amount: $amount, servingSize: food.servingSize)
                    
                    // 营养概览
                    NutritionOverviewCard(nutrition: actualNutrition, showingDetails: $showingNutritionDetails)
                    
                    // 备注
                    NotesCard(notes: $notes)
                    
                    Spacer(minLength: 100)
                }
                .padding()
            }
            .navigationTitle("食物详情")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("添加") {
                        addFoodRecord()
                    }
                    .fontWeight(.semibold)
                    .disabled(amount <= 0)
                }
            }
            .sheet(isPresented: $showingNutritionDetails) {
                NutritionDetailsView(nutrition: actualNutrition, amount: amount)
            }
        }
    }
    
    private func addFoodRecord() {
        let record = FoodRecord(
            food: food,
            amount: amount,
            mealType: mealType,
            timestamp: Date(),
            notes: notes.isEmpty ? nil : notes
        )
        
        onAddFood(record)
    }
}

struct FoodBasicInfoCard: View {
    let food: Food
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(food.name)
                        .font(.title2)
                        .fontWeight(.bold)
                    
                    if let brand = food.brand {
                        Text(brand)
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Text(food.category.rawValue)
                    .font(.caption)
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.green.opacity(0.2))
                    .foregroundColor(.green)
                    .cornerRadius(6)
            }
            
            if let servingSize = food.servingSize {
                Text("建议食用量: \(Int(servingSize))g")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct AmountSettingCard: View {
    @Binding var amount: Double
    let servingSize: Double?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("设置分量")
                .font(.headline)
                .fontWeight(.semibold)
            
            VStack(spacing: 12) {
                HStack {
                    Text("重量")
                        .font(.subheadline)
                    
                    Spacer()
                    
                    Text("\(Int(amount))g")
                        .font(.title3)
                        .fontWeight(.semibold)
                        .foregroundColor(.green)
                }
                
                Slider(value: $amount, in: 1...500, step: 1)
                    .accentColor(.green)
                
                HStack {
                    Text("1g")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text("500g")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            // 快速选择按钮
            HStack(spacing: 12) {
                QuickAmountButton(title: "50g", amount: 50, currentAmount: $amount)
                QuickAmountButton(title: "100g", amount: 100, currentAmount: $amount)
                
                if let serving = servingSize {
                    QuickAmountButton(title: "建议量", amount: serving, currentAmount: $amount)
                }
                
                QuickAmountButton(title: "200g", amount: 200, currentAmount: $amount)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct QuickAmountButton: View {
    let title: String
    let amount: Double
    @Binding var currentAmount: Double
    
    var body: some View {
        Button(action: {
            currentAmount = amount
        }) {
            Text(title)
                .font(.caption)
                .fontWeight(.medium)
                .padding(.horizontal, 12)
                .padding(.vertical, 6)
                .background(
                    currentAmount == amount ? Color.green : Color(.systemGray5)
                )
                .foregroundColor(
                    currentAmount == amount ? .white : .primary
                )
                .cornerRadius(8)
        }
    }
}

struct NutritionOverviewCard: View {
    let nutrition: NutritionFacts
    @Binding var showingDetails: Bool
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("营养成分")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("详细信息") {
                    showingDetails = true
                }
                .font(.caption)
                .foregroundColor(.blue)
            }
            
            // 主要营养素
            VStack(spacing: 12) {
                NutritionRow(
                    name: "能量",
                    value: "\(Int(nutrition.energyKcal))",
                    unit: "kcal",
                    color: .orange
                )
                
                NutritionRow(
                    name: "蛋白质",
                    value: String(format: "%.1f", nutrition.protein),
                    unit: "g",
                    color: .blue
                )
                
                NutritionRow(
                    name: "脂肪",
                    value: String(format: "%.1f", nutrition.fat),
                    unit: "g",
                    color: .yellow
                )
                
                NutritionRow(
                    name: "碳水化合物",
                    value: String(format: "%.1f", nutrition.carbohydrate),
                    unit: "g",
                    color: .green
                )
                
                NutritionRow(
                    name: "钠",
                    value: String(format: "%.0f", nutrition.sodium),
                    unit: "mg",
                    color: .red
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct NutritionRow: View {
    let name: String
    let value: String
    let unit: String
    let color: Color
    
    var body: some View {
        HStack {
            Circle()
                .fill(color)
                .frame(width: 8, height: 8)
            
            Text(name)
                .font(.subheadline)
            
            Spacer()
            
            Text("\(value) \(unit)")
                .font(.subheadline)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

struct NotesCard: View {
    @Binding var notes: String
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("备注")
                .font(.headline)
                .fontWeight(.semibold)
            
            TextField("添加备注信息（可选）", text: $notes, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(3...6)
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

#Preview {
    FoodDetailView(
        food: SampleData.sampleFemaleProfile.healthGoals.isEmpty ? 
              SampleFoodDatabase.commonFoods[0] : 
              SampleFoodDatabase.commonFoods[0],
        mealType: .lunch,
        onAddFood: { _ in }
    )
}
