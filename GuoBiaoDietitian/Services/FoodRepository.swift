import Foundation
import SwiftUI

class FoodRepository: ObservableObject {
    private let userDefaults = UserDefaults.standard
    private let recordsKey = "food_records"
    private let foodDatabaseKey = "food_database"
    
    // MARK: - Food Records Management
    func saveRecord(_ record: FoodRecord) {
        var records = loadAllRecords()
        records.append(record)
        saveAllRecords(records)
    }
    
    func loadRecords(from startDate: Date, to endDate: Date) -> [FoodRecord] {
        let allRecords = loadAllRecords()
        return allRecords.filter { record in
            record.timestamp >= startDate && record.timestamp < endDate
        }
    }
    
    func loadAllRecords() -> [FoodRecord] {
        guard let data = userDefaults.data(forKey: recordsKey),
              let records = try? JSONDecoder().decode([FoodRecord].self, from: data) else {
            return []
        }
        return records
    }
    
    private func saveAllRecords(_ records: [FoodRecord]) {
        if let data = try? JSONEncoder().encode(records) {
            userDefaults.set(data, forKey: recordsKey)
        }
    }
    
    func deleteRecord(_ recordId: UUID) {
        var records = loadAllRecords()
        records.removeAll { $0.id == recordId }
        saveAllRecords(records)
    }
    
    func updateRecord(_ record: FoodRecord) {
        var records = loadAllRecords()
        if let index = records.firstIndex(where: { $0.id == record.id }) {
            records[index] = record
            saveAllRecords(records)
        }
    }
    
    // MARK: - Food Database Management
    func searchFoods(query: String) -> [Food] {
        let allFoods = loadFoodDatabase()
        if query.isEmpty {
            return Array(allFoods.prefix(20)) // 返回前20个结果
        }
        
        return allFoods.filter { food in
            food.name.localizedCaseInsensitiveContains(query) ||
            food.brand?.localizedCaseInsensitiveContains(query) == true
        }
    }
    
    func getFoodByBarcode(_ barcode: String) -> Food? {
        let allFoods = loadFoodDatabase()
        return allFoods.first { $0.barcode == barcode }
    }
    
    func saveFood(_ food: Food) {
        var foods = loadFoodDatabase()
        if let index = foods.firstIndex(where: { $0.id == food.id }) {
            foods[index] = food
        } else {
            foods.append(food)
        }
        saveFoodDatabase(foods)
    }
    
    private func loadFoodDatabase() -> [Food] {
        // 首先尝试从 UserDefaults 加载用户自定义食物
        var foods: [Food] = []
        
        if let data = userDefaults.data(forKey: foodDatabaseKey),
           let userFoods = try? JSONDecoder().decode([Food].self, from: data) {
            foods.append(contentsOf: userFoods)
        }
        
        // 如果是首次启动，加载预设食物数据库
        if foods.isEmpty {
            foods = loadPresetFoodDatabase()
            saveFoodDatabase(foods)
        }
        
        return foods
    }
    
    private func saveFoodDatabase(_ foods: [Food]) {
        if let data = try? JSONEncoder().encode(foods) {
            userDefaults.set(data, forKey: foodDatabaseKey)
        }
    }
    
    // MARK: - Preset Food Database
    private func loadPresetFoodDatabase() -> [Food] {
        return [
            // 谷薯类
            Food(
                name: "白米饭",
                brand: nil,
                barcode: nil,
                category: .grains,
                nutritionFacts: NutritionFacts(
                    energy: 485, protein: 2.6, fat: 0.3, carbohydrate: 25.9, sodium: 1,
                    saturatedFat: 0.1, transFat: 0, cholesterol: 0, dietaryFiber: 0.3, sugar: 0.1,
                    vitaminA: 0, vitaminD: 0, vitaminE: 0.07, vitaminC: 0,
                    thiamine: 0.02, riboflavin: 0.01, niacin: 0.4, vitaminB6: 0.05, folate: 3, vitaminB12: 0,
                    calcium: 4, iron: 0.6, phosphorus: 34, potassium: 30, magnesium: 7,
                    zinc: 0.4, selenium: 0.4, copper: 0.05, manganese: 0.3, iodine: 0.3
                ),
                servingSize: 150,
                imageURL: nil,
                source: .database,
                createdAt: Date(),
                updatedAt: Date()
            ),
            
            Food(
                name: "全麦面包",
                brand: nil,
                barcode: nil,
                category: .grains,
                nutritionFacts: NutritionFacts(
                    energy: 1050, protein: 8.5, fat: 4.2, carbohydrate: 45.1, sodium: 400,
                    saturatedFat: 0.8, transFat: 0, cholesterol: 0, dietaryFiber: 6.8, sugar: 3.2,
                    vitaminA: 0, vitaminD: 0, vitaminE: 1.2, vitaminC: 0,
                    thiamine: 0.15, riboflavin: 0.08, niacin: 3.2, vitaminB6: 0.12, folate: 25, vitaminB12: 0,
                    calcium: 52, iron: 2.5, phosphorus: 115, potassium: 180, magnesium: 45,
                    zinc: 1.2, selenium: 12, copper: 0.15, manganese: 1.8, iodine: 2
                ),
                servingSize: 30,
                imageURL: nil,
                source: .database,
                createdAt: Date(),
                updatedAt: Date()
            ),
            
            // 蔬菜类
            Food(
                name: "西兰花",
                brand: nil,
                barcode: nil,
                category: .vegetables,
                nutritionFacts: NutritionFacts(
                    energy: 142, protein: 4.1, fat: 0.6, carbohydrate: 4.3, sodium: 18,
                    saturatedFat: 0.1, transFat: 0, cholesterol: 0, dietaryFiber: 2.6, sugar: 1.5,
                    vitaminA: 1202, vitaminD: 0, vitaminE: 1.5, vitaminC: 51,
                    thiamine: 0.07, riboflavin: 0.12, niacin: 1.1, vitaminB6: 0.18, folate: 65, vitaminB12: 0,
                    calcium: 67, iron: 1.4, phosphorus: 66, potassium: 288, magnesium: 25,
                    zinc: 0.4, selenium: 2.5, copper: 0.05, manganese: 0.2, iodine: 1
                ),
                servingSize: 100,
                imageURL: nil,
                source: .database,
                createdAt: Date(),
                updatedAt: Date()
            ),
            
            // 肉类
            Food(
                name: "鸡胸肉",
                brand: nil,
                barcode: nil,
                category: .meat,
                nutritionFacts: NutritionFacts(
                    energy: 695, protein: 23.3, fat: 1.9, carbohydrate: 0, sodium: 63,
                    saturatedFat: 0.5, transFat: 0, cholesterol: 58, dietaryFiber: 0, sugar: 0,
                    vitaminA: 9, vitaminD: 0.1, vitaminE: 0.2, vitaminC: 0,
                    thiamine: 0.07, riboflavin: 0.08, niacin: 11.6, vitaminB6: 0.6, folate: 4, vitaminB12: 0.3,
                    calcium: 9, iron: 0.7, phosphorus: 228, potassium: 251, magnesium: 25,
                    zinc: 0.9, selenium: 11.5, copper: 0.04, manganese: 0.02, iodine: 2.5
                ),
                servingSize: 100,
                imageURL: nil,
                source: .database,
                createdAt: Date(),
                updatedAt: Date()
            ),
            
            // 奶类
            Food(
                name: "纯牛奶",
                brand: nil,
                barcode: nil,
                category: .dairy,
                nutritionFacts: NutritionFacts(
                    energy: 272, protein: 3.4, fat: 3.8, carbohydrate: 5.0, sodium: 37,
                    saturatedFat: 2.3, transFat: 0, cholesterol: 15, dietaryFiber: 0, sugar: 5.0,
                    vitaminA: 24, vitaminD: 0.03, vitaminE: 0.21, vitaminC: 1,
                    thiamine: 0.03, riboflavin: 0.14, niacin: 0.1, vitaminB6: 0.03, folate: 5, vitaminB12: 0.3,
                    calcium: 104, iron: 0.3, phosphorus: 73, potassium: 109, magnesium: 11,
                    zinc: 0.4, selenium: 1.9, copper: 0.02, manganese: 0.004, iodine: 13
                ),
                servingSize: 250,
                imageURL: nil,
                source: .database,
                createdAt: Date(),
                updatedAt: Date()
            ),
            
            // 水果类
            Food(
                name: "苹果",
                brand: nil,
                barcode: nil,
                category: .vegetables,
                nutritionFacts: NutritionFacts(
                    energy: 218, protein: 0.2, fat: 0.2, carbohydrate: 13.5, sodium: 1,
                    saturatedFat: 0.03, transFat: 0, cholesterol: 0, dietaryFiber: 2.4, sugar: 10.4,
                    vitaminA: 3, vitaminD: 0, vitaminE: 0.18, vitaminC: 4.6,
                    thiamine: 0.02, riboflavin: 0.03, niacin: 0.1, vitaminB6: 0.04, folate: 3, vitaminB12: 0,
                    calcium: 6, iron: 0.1, phosphorus: 11, potassium: 107, magnesium: 5,
                    zinc: 0.05, selenium: 0, copper: 0.03, manganese: 0.04, iodine: 0.5
                ),
                servingSize: 150,
                imageURL: nil,
                source: .database,
                createdAt: Date(),
                updatedAt: Date()
            )
        ]
    }
}

class UserRepository {
    private let userDefaults = UserDefaults.standard
    private let userProfileKey = "user_profile"
    
    func saveUserProfile(_ profile: UserProfile) {
        if let data = try? JSONEncoder().encode(profile) {
            userDefaults.set(data, forKey: userProfileKey)
        }
    }
    
    func loadUserProfile() -> UserProfile? {
        guard let data = userDefaults.data(forKey: userProfileKey),
              let profile = try? JSONDecoder().decode(UserProfile.self, from: data) else {
            return nil
        }
        return profile
    }
    
    func deleteUserProfile() {
        userDefaults.removeObject(forKey: userProfileKey)
    }
}
