import Foundation
import Combine
import SwiftUI

@MainActor
class NutritionManager: ObservableObject {
    @Published var userProfile: UserProfile?
    @Published var todayRecords: [FoodRecord] = []
    @Published var weeklyRecords: [FoodRecord] = []
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private let foodRepository: FoodRepository
    private let userRepository: UserRepository
    private var cancellables = Set<AnyCancellable>()
    
    init(foodRepository: FoodRepository = FoodRepository(),
         userRepository: UserRepository = UserRepository()) {
        self.foodRepository = foodRepository
        self.userRepository = userRepository
        
        loadUserProfile()
        loadTodayRecords()
    }
    
    // MARK: - User Profile Management
    func loadUserProfile() {
        userProfile = userRepository.loadUserProfile()
    }
    
    func updateUserProfile(_ profile: UserProfile) {
        userProfile = profile
        userRepository.saveUserProfile(profile)
    }
    
    // MARK: - Food Records Management
    func loadTodayRecords() {
        let today = Calendar.current.startOfDay(for: Date())
        let tomorrow = Calendar.current.date(byAdding: .day, value: 1, to: today)!
        
        todayRecords = foodRepository.loadRecords(from: today, to: tomorrow)
    }
    
    func loadWeeklyRecords() {
        let calendar = Calendar.current
        let today = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today)!
        
        weeklyRecords = foodRepository.loadRecords(from: weekAgo, to: today)
    }
    
    func addFoodRecord(_ record: FoodRecord) {
        todayRecords.append(record)
        foodRepository.saveRecord(record)
        
        // 触发营养分析更新
        objectWillChange.send()
    }
    
    func deleteFoodRecord(_ record: FoodRecord) {
        todayRecords.removeAll { $0.id == record.id }
        foodRepository.deleteRecord(record.id)
    }
    
    // MARK: - Nutrition Analysis
    var todayNutritionSummary: NutritionSummary {
        calculateNutritionSummary(for: todayRecords)
    }
    
    var dailyRecommendation: ChineseDietaryStandards.DailyRecommendation? {
        guard let profile = userProfile else { return nil }
        return ChineseDietaryStandards.DietaryRecommendations.dailyRecommendation(
            for: profile.gender,
            activityLevel: profile.activityLevel
        )
    }
    
    private func calculateNutritionSummary(for records: [FoodRecord]) -> NutritionSummary {
        // Create a mutable accumulator for nutrition totals
        var totalEnergy: Double = 0
        var totalProtein: Double = 0
        var totalFat: Double = 0
        var totalCarbohydrate: Double = 0
        var totalSodium: Double = 0
        var totalSaturatedFat: Double = 0
        var totalTransFat: Double = 0
        var totalCholesterol: Double = 0
        var totalDietaryFiber: Double = 0
        var totalSugar: Double = 0
        var totalVitaminA: Double = 0
        var totalVitaminD: Double = 0
        var totalVitaminE: Double = 0
        var totalVitaminC: Double = 0
        var totalThiamine: Double = 0
        var totalRiboflavin: Double = 0
        var totalNiacin: Double = 0
        var totalVitaminB6: Double = 0
        var totalFolate: Double = 0
        var totalVitaminB12: Double = 0
        var totalCalcium: Double = 0
        var totalIron: Double = 0
        var totalPhosphorus: Double = 0
        var totalPotassium: Double = 0
        var totalMagnesium: Double = 0
        var totalZinc: Double = 0
        var totalSelenium: Double = 0
        var totalCopper: Double = 0
        var totalManganese: Double = 0
        var totalIodine: Double = 0

        // Accumulate nutrition values from all records
        for record in records {
            let nutrition = record.actualNutrition
            totalEnergy += nutrition.energy
            totalProtein += nutrition.protein
            totalFat += nutrition.fat
            totalCarbohydrate += nutrition.carbohydrate
            totalSodium += nutrition.sodium
            totalSaturatedFat += nutrition.saturatedFat ?? 0
            totalTransFat += nutrition.transFat ?? 0
            totalCholesterol += nutrition.cholesterol ?? 0
            totalDietaryFiber += nutrition.dietaryFiber ?? 0
            totalSugar += nutrition.sugar ?? 0
            totalVitaminA += nutrition.vitaminA ?? 0
            totalVitaminD += nutrition.vitaminD ?? 0
            totalVitaminE += nutrition.vitaminE ?? 0
            totalVitaminC += nutrition.vitaminC ?? 0
            totalThiamine += nutrition.thiamine ?? 0
            totalRiboflavin += nutrition.riboflavin ?? 0
            totalNiacin += nutrition.niacin ?? 0
            totalVitaminB6 += nutrition.vitaminB6 ?? 0
            totalFolate += nutrition.folate ?? 0
            totalVitaminB12 += nutrition.vitaminB12 ?? 0
            totalCalcium += nutrition.calcium ?? 0
            totalIron += nutrition.iron ?? 0
            totalPhosphorus += nutrition.phosphorus ?? 0
            totalPotassium += nutrition.potassium ?? 0
            totalMagnesium += nutrition.magnesium ?? 0
            totalZinc += nutrition.zinc ?? 0
            totalSelenium += nutrition.selenium ?? 0
            totalCopper += nutrition.copper ?? 0
            totalManganese += nutrition.manganese ?? 0
            totalIodine += nutrition.iodine ?? 0
        }

        // Create the final NutritionFacts with accumulated values
        let totalNutrition = NutritionFacts(
            energy: totalEnergy,
            protein: totalProtein,
            fat: totalFat,
            carbohydrate: totalCarbohydrate,
            sodium: totalSodium,
            saturatedFat: totalSaturatedFat,
            transFat: totalTransFat,
            cholesterol: totalCholesterol,
            dietaryFiber: totalDietaryFiber,
            sugar: totalSugar,
            vitaminA: totalVitaminA,
            vitaminD: totalVitaminD,
            vitaminE: totalVitaminE,
            vitaminC: totalVitaminC,
            thiamine: totalThiamine,
            riboflavin: totalRiboflavin,
            niacin: totalNiacin,
            vitaminB6: totalVitaminB6,
            folate: totalFolate,
            vitaminB12: totalVitaminB12,
            calcium: totalCalcium,
            iron: totalIron,
            phosphorus: totalPhosphorus,
            potassium: totalPotassium,
            magnesium: totalMagnesium,
            zinc: totalZinc,
            selenium: totalSelenium,
            copper: totalCopper,
            manganese: totalManganese,
            iodine: totalIodine
        )
        
        return NutritionSummary(
            totalNutrition: totalNutrition,
            recommendation: dailyRecommendation,
            mealDistribution: calculateMealDistribution(records)
        )
    }
    
    private func calculateMealDistribution(_ records: [FoodRecord]) -> [FoodRecord.MealType: Double] {
        var distribution: [FoodRecord.MealType: Double] = [:]
        
        for mealType in FoodRecord.MealType.allCases {
            let mealRecords = records.filter { $0.mealType == mealType }
            let totalEnergy = mealRecords.reduce(0) { $0 + $1.actualNutrition.energyKcal }
            distribution[mealType] = totalEnergy
        }
        
        return distribution
    }
    
    // MARK: - Nutrition Status Assessment
    func getNutritionStatus(for nutrient: String, amount: Double) -> NutritionStatus {
        guard let nrv = ChineseDietaryStandards.nrvValues[nutrient] else {
            return .unknown
        }
        
        let percentage = (amount / nrv) * 100
        
        switch percentage {
        case 0..<50:
            return .deficient
        case 50..<80:
            return .insufficient
        case 80..<120:
            return .adequate
        case 120..<150:
            return .high
        default:
            return .excessive
        }
    }
    
    func getMacronutrientBalance() -> MacronutrientBalance? {
        guard let recommendation = dailyRecommendation else { return nil }
        
        let summary = todayNutritionSummary
        let totalEnergy = summary.totalNutrition.energyKcal
        
        guard totalEnergy > 0 else { return nil }
        
        let proteinPercentage = (summary.totalNutrition.protein * 4) / totalEnergy * 100
        let fatPercentage = (summary.totalNutrition.fat * 9) / totalEnergy * 100
        let carbohydratePercentage = (summary.totalNutrition.carbohydrate * 4) / totalEnergy * 100
        
        return MacronutrientBalance(
            proteinPercentage: proteinPercentage,
            fatPercentage: fatPercentage,
            carbohydratePercentage: carbohydratePercentage,
            isBalanced: isBalanced(protein: proteinPercentage, fat: fatPercentage, carb: carbohydratePercentage)
        )
    }
    
    private func isBalanced(protein: Double, fat: Double, carb: Double) -> Bool {
        // 中国膳食指南推荐比例：蛋白质10-15%，脂肪20-30%，碳水化合物50-65%
        return protein >= 10 && protein <= 15 &&
               fat >= 20 && fat <= 30 &&
               carb >= 50 && carb <= 65
    }
}

// MARK: - Supporting Types
struct NutritionSummary {
    let totalNutrition: NutritionFacts
    let recommendation: ChineseDietaryStandards.DailyRecommendation?
    let mealDistribution: [FoodRecord.MealType: Double]
    
    var energyProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.energyKcal / rec.energy
    }
    
    var proteinProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.protein / rec.protein
    }
    
    var fatProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.fat / rec.fat
    }
    
    var carbohydrateProgress: Double {
        guard let rec = recommendation else { return 0 }
        return totalNutrition.carbohydrate / rec.carbohydrate
    }
}

enum NutritionStatus: String, CaseIterable {
    case deficient = "缺乏"
    case insufficient = "不足"
    case adequate = "充足"
    case high = "偏高"
    case excessive = "过量"
    case unknown = "未知"
    
    var color: Color {
        switch self {
        case .deficient, .excessive: return .red
        case .insufficient, .high: return .orange
        case .adequate: return .green
        case .unknown: return .gray
        }
    }
}

struct MacronutrientBalance {
    let proteinPercentage: Double
    let fatPercentage: Double
    let carbohydratePercentage: Double
    let isBalanced: Bool
}
