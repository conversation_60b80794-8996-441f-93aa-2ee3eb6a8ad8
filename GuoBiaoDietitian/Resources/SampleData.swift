import Foundation

/// 示例数据生成器，用于演示和测试
struct SampleData {
    
    // MARK: - 示例用户资料
    static let sampleUserProfile = UserProfile(
        name: "张三",
        gender: .male,
        birthDate: Calendar.current.date(byAdding: .year, value: -30, to: Date()) ?? Date(),
        height: 175.0,
        weight: 70.0,
        activityLevel: .moderatelyActive,
        healthGoals: [.maintenance, .healthImprovement],
        medicalConditions: [],
        allergies: [],
        createdAt: Date(),
        updatedAt: Date()
    )
    
    static let sampleFemaleProfile = UserProfile(
        name: "李四",
        gender: .female,
        birthDate: Calendar.current.date(byAdding: .year, value: -28, to: Date()) ?? Date(),
        height: 162.0,
        weight: 55.0,
        activityLevel: .lightlyActive,
        healthGoals: [.weightLoss],
        medicalConditions: [],
        allergies: ["花生", "海鲜"],
        createdAt: Date(),
        updatedAt: Date()
    )
    
    // MARK: - 示例食物记录
    static func generateSampleFoodRecords() -> [FoodRecord] {
        let foods = SampleFoodDatabase.commonFoods
        var records: [FoodRecord] = []
        
        let calendar = Calendar.current
        let today = Date()
        
        // 今天的记录
        records.append(FoodRecord(
            food: foods[0], // 白米饭
            amount: 150,
            mealType: .breakfast,
            timestamp: calendar.date(byAdding: .hour, value: -10, to: today) ?? today,
            notes: nil
        ))
        
        records.append(FoodRecord(
            food: foods[2], // 西兰花
            amount: 100,
            mealType: .lunch,
            timestamp: calendar.date(byAdding: .hour, value: -5, to: today) ?? today,
            notes: "清炒"
        ))
        
        records.append(FoodRecord(
            food: foods[3], // 鸡胸肉
            amount: 120,
            mealType: .lunch,
            timestamp: calendar.date(byAdding: .hour, value: -5, to: today) ?? today,
            notes: "水煮"
        ))
        
        records.append(FoodRecord(
            food: foods[4], // 纯牛奶
            amount: 250,
            mealType: .snack,
            timestamp: calendar.date(byAdding: .hour, value: -2, to: today) ?? today,
            notes: nil
        ))
        
        // 昨天的记录
        let yesterday = calendar.date(byAdding: .day, value: -1, to: today) ?? today
        
        records.append(FoodRecord(
            food: foods[1], // 全麦面包
            amount: 60,
            mealType: .breakfast,
            timestamp: calendar.date(byAdding: .hour, value: 8, to: yesterday.startOfDay) ?? yesterday,
            notes: "配果酱"
        ))
        
        records.append(FoodRecord(
            food: foods[5], // 苹果
            amount: 150,
            mealType: .snack,
            timestamp: calendar.date(byAdding: .hour, value: 15, to: yesterday.startOfDay) ?? yesterday,
            notes: nil
        ))
        
        // 一周前的记录
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today) ?? today
        
        for i in 0..<5 {
            let date = calendar.date(byAdding: .day, value: i, to: weekAgo) ?? weekAgo
            
            records.append(FoodRecord(
                food: foods[i % foods.count],
                amount: Double.random(in: 80...200),
                mealType: FoodRecord.MealType.allCases.randomElement() ?? .lunch,
                timestamp: calendar.date(byAdding: .hour, value: Int.random(in: 8...20), to: date.startOfDay) ?? date,
                notes: nil
            ))
        }
        
        return records
    }
    
    // MARK: - 示例营养分析数据
    static func generateSampleNutritionSummary() -> NutritionSummary {
        let totalNutrition = NutritionFacts(
            energy: 7500, // 1790 kcal
            protein: 75,
            fat: 60,
            carbohydrate: 220,
            sodium: 1800,
            saturatedFat: 18,
            transFat: 0,
            cholesterol: 120,
            dietaryFiber: 28,
            sugar: 45,
            vitaminA: 650,
            vitaminD: 3.2,
            vitaminE: 12,
            vitaminC: 85,
            thiamine: 1.1,
            riboflavin: 1.2,
            niacin: 16,
            vitaminB6: 1.8,
            folate: 320,
            vitaminB12: 2.8,
            calcium: 750,
            iron: 14,
            phosphorus: 680,
            potassium: 2800,
            magnesium: 280,
            zinc: 9.5,
            selenium: 45,
            copper: 0.9,
            manganese: 2.1,
            iodine: 120
        )
        
        let recommendation = ChineseDietaryStandards.DietaryRecommendations.dailyRecommendation(
            for: .male,
            activityLevel: .moderatelyActive
        )
        
        let mealDistribution: [FoodRecord.MealType: Double] = [
            .breakfast: 450, // kcal
            .lunch: 680,
            .dinner: 520,
            .snack: 140
        ]
        
        return NutritionSummary(
            totalNutrition: totalNutrition,
            recommendation: recommendation,
            mealDistribution: mealDistribution
        )
    }
    
    // MARK: - 示例趋势数据
    static func generateSampleTrendData(days: Int = 7) -> [DailyNutritionData] {
        var data: [DailyNutritionData] = []
        let calendar = Calendar.current
        let today = Date()
        
        for i in 0..<days {
            let date = calendar.date(byAdding: .day, value: -i, to: today) ?? today
            
            data.append(DailyNutritionData(
                date: date,
                energy: Double.random(in: 1600...2400),
                protein: Double.random(in: 60...100),
                fat: Double.random(in: 45...85),
                carbohydrate: Double.random(in: 200...350),
                sodium: Double.random(in: 1200...2800)
            ))
        }
        
        return data.reversed()
    }
    
    // MARK: - 示例AI对话
    static let sampleChatMessages: [ChatMessage] = [
        ChatMessage(
            content: "您好！我是基于中国国标的AI营养助手。我可以帮您分析营养摄入情况，解释国标要求，并提供专业的膳食建议。",
            isFromUser: false,
            timestamp: Calendar.current.date(byAdding: .minute, value: -30, to: Date()) ?? Date()
        ),
        ChatMessage(
            content: "我今天的脂肪摄入是不是超标了？",
            isFromUser: true,
            timestamp: Calendar.current.date(byAdding: .minute, value: -25, to: Date()) ?? Date()
        ),
        ChatMessage(
            content: """
            根据您今日的摄入情况，脂肪摄入量为 60g，在推荐量范围内。
            
            **国标依据：**
            根据《中国居民膳食指南2022》，成年人脂肪供能比应占总能量的20-30%。
            
            您今日的脂肪供能比约为30%，处于推荐范围的上限，建议明天适当减少烹调油的使用。
            """,
            isFromUser: false,
            timestamp: Calendar.current.date(byAdding: .minute, value: -20, to: Date()) ?? Date()
        ),
        ChatMessage(
            content: "那我应该怎么减少脂肪摄入呢？",
            isFromUser: true,
            timestamp: Calendar.current.date(byAdding: .minute, value: -15, to: Date()) ?? Date()
        ),
        ChatMessage(
            content: """
            以下是减少脂肪摄入的实用建议：
            
            **烹调方式：**
            • 多用蒸、煮、炖、烤等少油烹调方式
            • 减少油炸、油煎食品
            • 使用不粘锅减少用油量
            
            **食材选择：**
            • 选择瘦肉，去除可见脂肪
            • 去皮禽肉
            • 多选择鱼虾等水产品
            
            **调味品：**
            • 控制烹调油用量，每日25-30g
            • 少用含油调味品
            
            这些建议基于《中国居民膳食指南2022》的科学指导。
            """,
            isFromUser: false,
            timestamp: Calendar.current.date(byAdding: .minute, value: -10, to: Date()) ?? Date()
        )
    ]
    
    // MARK: - 示例营养风险
    static let sampleNutritionRisks: [NutritionRisk] = [
        .insufficientFiber,
        .excessiveSodium
    ]
    
    // MARK: - 示例食物组摄入
    static let sampleFoodGroupIntake: [String: Double] = [
        "谷薯类": 280,
        "蔬菜水果类": 420,
        "畜禽肉类": 150,
        "水产类": 60,
        "蛋类": 50,
        "奶类": 250,
        "大豆坚果类": 30,
        "烹调油": 28
    ]
    
    // MARK: - 示例目标达成情况
    static let sampleGoalAchievement: [String: (achieved: Int, total: Int)] = [
        "能量目标": (5, 7),
        "蛋白质目标": (6, 7),
        "营养均衡": (4, 7),
        "食物多样性": (3, 7)
    ]
}

// MARK: - 示例食物数据库
struct SampleFoodDatabase {
    static let commonFoods: [Food] = [
        // 已在 FoodRepository 中定义的食物
        Food(
            name: "白米饭",
            brand: nil,
            barcode: nil,
            category: .grains,
            nutritionFacts: NutritionFacts(
                energy: 485, protein: 2.6, fat: 0.3, carbohydrate: 25.9, sodium: 1,
                saturatedFat: 0.1, transFat: 0, cholesterol: 0, dietaryFiber: 0.3, sugar: 0.1,
                vitaminA: 0, vitaminD: 0, vitaminE: 0.07, vitaminC: 0,
                thiamine: 0.02, riboflavin: 0.01, niacin: 0.4, vitaminB6: 0.05, folate: 3, vitaminB12: 0,
                calcium: 4, iron: 0.6, phosphorus: 34, potassium: 30, magnesium: 7,
                zinc: 0.4, selenium: 0.4, copper: 0.05, manganese: 0.3, iodine: 0.3
            ),
            servingSize: 150,
            imageURL: nil,
            source: .database,
            createdAt: Date(),
            updatedAt: Date()
        ),
        
        Food(
            name: "全麦面包",
            brand: nil,
            barcode: nil,
            category: .grains,
            nutritionFacts: NutritionFacts(
                energy: 1050, protein: 8.5, fat: 4.2, carbohydrate: 45.1, sodium: 400,
                saturatedFat: 0.8, transFat: 0, cholesterol: 0, dietaryFiber: 6.8, sugar: 3.2,
                vitaminA: 0, vitaminD: 0, vitaminE: 1.2, vitaminC: 0,
                thiamine: 0.15, riboflavin: 0.08, niacin: 3.2, vitaminB6: 0.12, folate: 25, vitaminB12: 0,
                calcium: 52, iron: 2.5, phosphorus: 115, potassium: 180, magnesium: 45,
                zinc: 1.2, selenium: 12, copper: 0.15, manganese: 1.8, iodine: 2
            ),
            servingSize: 30,
            imageURL: nil,
            source: .database,
            createdAt: Date(),
            updatedAt: Date()
        ),
        
        Food(
            name: "西兰花",
            brand: nil,
            barcode: nil,
            category: .vegetables,
            nutritionFacts: NutritionFacts(
                energy: 142, protein: 4.1, fat: 0.6, carbohydrate: 4.3, sodium: 18,
                saturatedFat: 0.1, transFat: 0, cholesterol: 0, dietaryFiber: 2.6, sugar: 1.5,
                vitaminA: 1202, vitaminD: 0, vitaminE: 1.5, vitaminC: 51,
                thiamine: 0.07, riboflavin: 0.12, niacin: 1.1, vitaminB6: 0.18, folate: 65, vitaminB12: 0,
                calcium: 67, iron: 1.4, phosphorus: 66, potassium: 288, magnesium: 25,
                zinc: 0.4, selenium: 2.5, copper: 0.05, manganese: 0.2, iodine: 1
            ),
            servingSize: 100,
            imageURL: nil,
            source: .database,
            createdAt: Date(),
            updatedAt: Date()
        ),
        
        Food(
            name: "鸡胸肉",
            brand: nil,
            barcode: nil,
            category: .meat,
            nutritionFacts: NutritionFacts(
                energy: 695, protein: 23.3, fat: 1.9, carbohydrate: 0, sodium: 63,
                saturatedFat: 0.5, transFat: 0, cholesterol: 58, dietaryFiber: 0, sugar: 0,
                vitaminA: 9, vitaminD: 0.1, vitaminE: 0.2, vitaminC: 0,
                thiamine: 0.07, riboflavin: 0.08, niacin: 11.6, vitaminB6: 0.6, folate: 4, vitaminB12: 0.3,
                calcium: 9, iron: 0.7, phosphorus: 228, potassium: 251, magnesium: 25,
                zinc: 0.9, selenium: 11.5, copper: 0.04, manganese: 0.02, iodine: 2.5
            ),
            servingSize: 100,
            imageURL: nil,
            source: .database,
            createdAt: Date(),
            updatedAt: Date()
        ),
        
        Food(
            name: "纯牛奶",
            brand: nil,
            barcode: nil,
            category: .dairy,
            nutritionFacts: NutritionFacts(
                energy: 272, protein: 3.4, fat: 3.8, carbohydrate: 5.0, sodium: 37,
                saturatedFat: 2.3, transFat: 0, cholesterol: 15, dietaryFiber: 0, sugar: 5.0,
                vitaminA: 24, vitaminD: 0.03, vitaminE: 0.21, vitaminC: 1,
                thiamine: 0.03, riboflavin: 0.14, niacin: 0.1, vitaminB6: 0.03, folate: 5, vitaminB12: 0.3,
                calcium: 104, iron: 0.3, phosphorus: 73, potassium: 109, magnesium: 11,
                zinc: 0.4, selenium: 1.9, copper: 0.02, manganese: 0.004, iodine: 13
            ),
            servingSize: 250,
            imageURL: nil,
            source: .database,
            createdAt: Date(),
            updatedAt: Date()
        ),
        
        Food(
            name: "苹果",
            brand: nil,
            barcode: nil,
            category: .vegetables,
            nutritionFacts: NutritionFacts(
                energy: 218, protein: 0.2, fat: 0.2, carbohydrate: 13.5, sodium: 1,
                saturatedFat: 0.03, transFat: 0, cholesterol: 0, dietaryFiber: 2.4, sugar: 10.4,
                vitaminA: 3, vitaminD: 0, vitaminE: 0.18, vitaminC: 4.6,
                thiamine: 0.02, riboflavin: 0.03, niacin: 0.1, vitaminB6: 0.04, folate: 3, vitaminB12: 0,
                calcium: 6, iron: 0.1, phosphorus: 11, potassium: 107, magnesium: 5,
                zinc: 0.05, selenium: 0, copper: 0.03, manganese: 0.04, iodine: 0.5
            ),
            servingSize: 150,
            imageURL: nil,
            source: .database,
            createdAt: Date(),
            updatedAt: Date()
        )
    ]
}
