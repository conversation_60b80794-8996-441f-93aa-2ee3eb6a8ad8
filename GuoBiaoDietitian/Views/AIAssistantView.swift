import SwiftUI

struct AIAssistantView: View {
    @State private var messages: [ChatMessage] = []
    @State private var inputText = ""
    @State private var isLoading = false
    @EnvironmentObject var nutritionManager: NutritionManager
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 聊天消息列表
                ScrollViewReader { proxy in
                    ScrollView {
                        LazyVStack(spacing: 16) {
                            // 欢迎消息
                            if messages.isEmpty {
                                WelcomeMessageView()
                            }
                            
                            // 聊天消息
                            ForEach(messages) { message in
                                ChatMessageView(message: message)
                                    .id(message.id)
                            }
                            
                            // 加载指示器
                            if isLoading {
                                LoadingMessageView()
                            }
                        }
                        .padding()
                    }
                    .onChange(of: messages.count) {
                        if let lastMessage = messages.last {
                            withAnimation(.easeInOut(duration: 0.5)) {
                                proxy.scrollTo(lastMessage.id, anchor: .bottom)
                            }
                        }
                    }
                }
                
                Divider()
                
                // 输入区域
                ChatInputView(
                    inputText: $inputText,
                    isLoading: isLoading,
                    onSend: sendMessage
                )
            }
            .navigationTitle("AI 营养助手")
        }
    }
    
    private func sendMessage() {
        guard !inputText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }
        
        let userMessage = ChatMessage(
            content: inputText,
            isFromUser: true,
            timestamp: Date()
        )
        
        messages.append(userMessage)
        let userInput = inputText
        inputText = ""
        isLoading = true
        
        // 模拟 AI 响应
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) {
            let aiResponse = generateAIResponse(for: userInput)
            let aiMessage = ChatMessage(
                content: aiResponse,
                isFromUser: false,
                timestamp: Date()
            )
            
            messages.append(aiMessage)
            isLoading = false
        }
    }
    
    private func generateAIResponse(for input: String) -> String {
        let lowercaseInput = input.lowercased()
        
        // 基于关键词的简单响应逻辑
        if lowercaseInput.contains("脂肪") || lowercaseInput.contains("超标") {
            return generateFatAnalysisResponse()
        } else if lowercaseInput.contains("蛋白质") || lowercaseInput.contains("不足") {
            return generateProteinAnalysisResponse()
        } else if lowercaseInput.contains("钠") || lowercaseInput.contains("盐") {
            return generateSodiumAnalysisResponse()
        } else if lowercaseInput.contains("建议") || lowercaseInput.contains("推荐") {
            return generateRecommendationResponse()
        } else if lowercaseInput.contains("国标") || lowercaseInput.contains("标准") {
            return generateStandardResponse()
        } else {
            return generateGeneralResponse()
        }
    }
    
    private func generateFatAnalysisResponse() -> String {
        let summary = nutritionManager.todayNutritionSummary
        let fatIntake = summary.totalNutrition.fat
        let recommendation = summary.recommendation?.fat ?? 60
        
        if fatIntake > recommendation {
            return """
            根据您今日的摄入情况，脂肪摄入量为 \(Int(fatIntake))g，超过了推荐量 \(Int(recommendation))g。
            
            **国标依据：**
            根据《中国居民膳食指南2022》，成年人脂肪供能比应占总能量的20-30%。
            
            **建议：**
            1. 减少烹调油用量，每日不超过25-30g
            2. 选择瘦肉，去除可见脂肪
            3. 少吃油炸、油煎食品
            4. 增加蒸、煮、炖等烹调方式
            
            明天可以适当调整饮食结构，多选择低脂食物。
            """
        } else {
            return """
            您今日的脂肪摄入量为 \(Int(fatIntake))g，在推荐范围内，很好！
            
            继续保持均衡的脂肪摄入，注意选择优质脂肪来源如坚果、深海鱼类等。
            """
        }
    }
    
    private func generateProteinAnalysisResponse() -> String {
        let summary = nutritionManager.todayNutritionSummary
        let proteinIntake = summary.totalNutrition.protein
        let recommendation = summary.recommendation?.protein ?? 60
        
        return """
        您今日的蛋白质摄入量为 \(Int(proteinIntake))g，推荐量为 \(Int(recommendation))g。
        
        **国标依据：**
        根据《中国居民膳食指南2022》，成年人蛋白质供能比应占总能量的10-15%。
        
        **优质蛋白质来源：**
        • 瘦肉、禽肉、鱼虾
        • 蛋类、奶类
        • 大豆及其制品
        
        建议每餐都要有适量的蛋白质食物。
        """
    }
    
    private func generateSodiumAnalysisResponse() -> String {
        let summary = nutritionManager.todayNutritionSummary
        let sodiumIntake = summary.totalNutrition.sodium
        
        return """
        您今日的钠摄入量为 \(Int(sodiumIntake))mg。
        
        **国标依据：**
        根据GB 28050-2011《预包装食品营养标签通则》，成年人钠的营养素参考值(NRV)为2000mg/天。
        世界卫生组织建议每日钠摄入量不超过2000mg（相当于5g盐）。
        
        **减盐建议：**
        1. 烹调时少放盐，可用香料、柠檬汁等调味
        2. 少吃咸菜、腌制品
        3. 选择低钠食品
        4. 注意隐形盐：面包、饼干等加工食品
        
        \(sodiumIntake > 2000 ? "今日钠摄入偏高，明天注意控制。" : "今日钠摄入在合理范围内。")
        """
    }
    
    private func generateRecommendationResponse() -> String {
        return """
        基于您的营养状况，我为您提供以下建议：
        
        **膳食搭配原则：**
        1. 食物多样，谷类为主
        2. 吃动平衡，健康体重
        3. 多吃蔬果、奶类、全谷、大豆
        4. 适量吃鱼、禽、蛋、瘦肉
        5. 少盐少油，控糖限酒
        
        **今日建议：**
        • 增加深色蔬菜摄入
        • 选择全谷物主食
        • 保证充足饮水
        
        这些建议基于《中国居民膳食指南2022》制定。
        """
    }
    
    private func generateStandardResponse() -> String {
        return """
        我的营养分析基于以下权威标准：
        
        **主要依据：**
        1. **GB 28050-2011** 《预包装食品营养标签通则》
           - 营养素参考值(NRV)标准
           - 营养成分标示要求
        
        2. **《中国居民膳食指南2022》**
           - 膳食推荐摄入量
           - 食物搭配原则
        
        3. **《成人肥胖食养指南2024》**
           - 体重管理建议
           - 能量平衡指导
        
        所有营养评估和建议都严格遵循这些国家标准，确保科学性和权威性。
        """
    }
    
    private func generateGeneralResponse() -> String {
        return """
        您好！我是基于中国国标的AI营养助手。
        
        我可以帮您：
        • 分析今日营养摄入情况
        • 解释为什么某些营养素超标或不足
        • 提供基于国标的膳食建议
        • 回答营养相关问题
        
        请告诉我您想了解什么，比如：
        "我今天脂肪摄入超标了吗？"
        "如何增加蛋白质摄入？"
        "国标对钠的要求是什么？"
        """
    }
}

struct ChatMessage: Identifiable {
    let id = UUID()
    let content: String
    let isFromUser: Bool
    let timestamp: Date
}

struct WelcomeMessageView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "brain.head.profile")
                .font(.system(size: 50))
                .foregroundColor(.green)
            
            Text("AI 营养助手")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("基于中国国标的专业营养分析")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            VStack(alignment: .leading, spacing: 8) {
                Text("我可以帮您：")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text("• 分析营养摄入状况")
                    Text("• 解释国标营养要求")
                    Text("• 提供膳食改善建议")
                    Text("• 回答营养相关问题")
                }
                .font(.caption)
                .foregroundColor(.secondary)
            }
            .padding()
            .background(Color(.secondarySystemBackground))
            .cornerRadius(10)
        }
        .padding(.vertical, 20)
    }
}

struct ChatMessageView: View {
    let message: ChatMessage
    
    var body: some View {
        HStack {
            if message.isFromUser {
                Spacer()
                
                VStack(alignment: .trailing, spacing: 4) {
                    Text(message.content)
                        .padding()
                        .background(Color.blue)
                        .foregroundColor(.white)
                        .cornerRadius(16)
                        .frame(maxWidth: .infinity * 0.8, alignment: .trailing)
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            } else {
                VStack(alignment: .leading, spacing: 4) {
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: "brain.head.profile")
                            .font(.caption)
                            .foregroundColor(.green)
                            .padding(6)
                            .background(Color.green.opacity(0.1))
                            .cornerRadius(8)
                        
                        Text(message.content)
                            .padding()
                            .background(Color(.secondarySystemBackground))
                            .cornerRadius(16)
                            .frame(maxWidth: .infinity * 0.8, alignment: .leading)
                    }
                    
                    Text(message.timestamp, style: .time)
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .padding(.leading, 32)
                }
                
                Spacer()
            }
        }
    }
}

struct LoadingMessageView: View {
    var body: some View {
        HStack {
            HStack(alignment: .top, spacing: 8) {
                Image(systemName: "brain.head.profile")
                    .font(.caption)
                    .foregroundColor(.green)
                    .padding(6)
                    .background(Color.green.opacity(0.1))
                    .cornerRadius(8)
                
                HStack(spacing: 4) {
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(Color.secondary)
                            .frame(width: 8, height: 8)
                            .scaleEffect(1.0)
                            .animation(
                                Animation.easeInOut(duration: 0.6)
                                    .repeatForever()
                                    .delay(Double(index) * 0.2),
                                value: UUID()
                            )
                    }
                }
                .padding()
                .background(Color(.secondarySystemBackground))
                .cornerRadius(16)
            }
            
            Spacer()
        }
    }
}

struct ChatInputView: View {
    @Binding var inputText: String
    let isLoading: Bool
    let onSend: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            TextField("输入您的问题...", text: $inputText, axis: .vertical)
                .textFieldStyle(RoundedBorderTextFieldStyle())
                .lineLimit(1...4)
                .onSubmit {
                    if !isLoading {
                        onSend()
                    }
                }
            
            Button(action: onSend) {
                Image(systemName: "paperplane.fill")
                    .foregroundColor(inputText.isEmpty || isLoading ? .secondary : .blue)
            }
            .disabled(inputText.isEmpty || isLoading)
        }
        .padding()
    }
}

#Preview {
    AIAssistantView()
        .environmentObject(NutritionManager())
}
