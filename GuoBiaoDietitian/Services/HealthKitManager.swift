import Foundation
import HealthKit
import Combine

@MainActor
class HealthKitManager: ObservableObject {
    @Published var isAuthorized = false
    @Published var weight: Double?
    @Published var height: Double?
    @Published var stepCount: Int?
    @Published var activeEnergyBurned: Double?
    @Published var errorMessage: String?
    
    private let healthStore = HKHealthStore()
    private var cancellables = Set<AnyCancellable>()
    
    // 需要读取的健康数据类型
    private let readTypes: Set<HKObjectType> = [
        HKObjectType.quantityType(forIdentifier: .bodyMass)!,
        HKObjectType.quantityType(forIdentifier: .height)!,
        HKObjectType.quantityType(forIdentifier: .stepCount)!,
        HKObjectType.quantityType(forIdentifier: .activeEnergyBurned)!,
        HKObjectType.quantityType(forIdentifier: .basalEnergyBurned)!,
        HKObjectType.quantityType(forIdentifier: .dietaryEnergyConsumed)!,
        HKObjectType.quantityType(forIdentifier: .dietaryProtein)!,
        HKObjectType.quantityType(forIdentifier: .dietaryFatTotal)!,
        HKObjectType.quantityType(forIdentifier: .dietaryCarbohydrates)!,
        HKObjectType.quantityType(forIdentifier: .dietarySodium)!
    ]
    
    // 需要写入的健康数据类型
    private let writeTypes: Set<HKSampleType> = [
        HKObjectType.quantityType(forIdentifier: .dietaryEnergyConsumed)!,
        HKObjectType.quantityType(forIdentifier: .dietaryProtein)!,
        HKObjectType.quantityType(forIdentifier: .dietaryFatTotal)!,
        HKObjectType.quantityType(forIdentifier: .dietaryCarbohydrates)!,
        HKObjectType.quantityType(forIdentifier: .dietarySodium)!,
        HKObjectType.quantityType(forIdentifier: .dietaryFiber)!,
        HKObjectType.quantityType(forIdentifier: .dietarySugar)!,
        HKObjectType.quantityType(forIdentifier: .dietaryCalcium)!,
        HKObjectType.quantityType(forIdentifier: .dietaryIron)!,
        HKObjectType.quantityType(forIdentifier: .dietaryVitaminC)!
    ]
    
    init() {
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    func requestAuthorization(completion: @escaping (Bool) -> Void) {
        guard HKHealthStore.isHealthDataAvailable() else {
            DispatchQueue.main.async {
                self.errorMessage = "健康数据在此设备上不可用"
                completion(false)
            }
            return
        }
        
        healthStore.requestAuthorization(toShare: writeTypes, read: readTypes) { [weak self] success, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "HealthKit 授权失败: \(error.localizedDescription)"
                    completion(false)
                } else {
                    self?.isAuthorized = success
                    if success {
                        self?.loadHealthData()
                    }
                    completion(success)
                }
            }
        }
    }
    
    private func checkAuthorizationStatus() {
        guard let weightType = HKObjectType.quantityType(forIdentifier: .bodyMass) else { return }
        
        let status = healthStore.authorizationStatus(for: weightType)
        isAuthorized = status == .sharingAuthorized
        
        if isAuthorized {
            loadHealthData()
        }
    }
    
    // MARK: - Data Loading
    func loadHealthData() {
        loadWeight()
        loadHeight()
        loadStepCount()
        loadActiveEnergyBurned()
    }
    
    private func loadWeight() {
        guard let weightType = HKQuantityType.quantityType(forIdentifier: .bodyMass) else { return }
        
        let query = HKSampleQuery(
            sampleType: weightType,
            predicate: nil,
            limit: 1,
            sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: false)]
        ) { [weak self] _, samples, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "加载体重数据失败: \(error.localizedDescription)"
                    return
                }
                
                if let sample = samples?.first as? HKQuantitySample {
                    let weightInKg = sample.quantity.doubleValue(for: HKUnit.gramUnit(with: .kilo))
                    self?.weight = weightInKg
                }
            }
        }
        
        healthStore.execute(query)
    }
    
    private func loadHeight() {
        guard let heightType = HKQuantityType.quantityType(forIdentifier: .height) else { return }
        
        let query = HKSampleQuery(
            sampleType: heightType,
            predicate: nil,
            limit: 1,
            sortDescriptors: [NSSortDescriptor(key: HKSampleSortIdentifierStartDate, ascending: false)]
        ) { [weak self] _, samples, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "加载身高数据失败: \(error.localizedDescription)"
                    return
                }
                
                if let sample = samples?.first as? HKQuantitySample {
                    let heightInCm = sample.quantity.doubleValue(for: HKUnit.meterUnit(with: .centi))
                    self?.height = heightInCm
                }
            }
        }
        
        healthStore.execute(query)
    }
    
    private func loadStepCount() {
        guard let stepType = HKQuantityType.quantityType(forIdentifier: .stepCount) else { return }
        
        let calendar = Calendar.current
        let now = Date()
        let startOfDay = calendar.startOfDay(for: now)
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: now, options: .strictStartDate)
        
        let query = HKStatisticsQuery(
            quantityType: stepType,
            quantitySamplePredicate: predicate,
            options: .cumulativeSum
        ) { [weak self] _, result, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "加载步数数据失败: \(error.localizedDescription)"
                    return
                }
                
                if let sum = result?.sumQuantity() {
                    let steps = Int(sum.doubleValue(for: HKUnit.count()))
                    self?.stepCount = steps
                }
            }
        }
        
        healthStore.execute(query)
    }
    
    private func loadActiveEnergyBurned() {
        guard let energyType = HKQuantityType.quantityType(forIdentifier: .activeEnergyBurned) else { return }
        
        let calendar = Calendar.current
        let now = Date()
        let startOfDay = calendar.startOfDay(for: now)
        let predicate = HKQuery.predicateForSamples(withStart: startOfDay, end: now, options: .strictStartDate)
        
        let query = HKStatisticsQuery(
            quantityType: energyType,
            quantitySamplePredicate: predicate,
            options: .cumulativeSum
        ) { [weak self] _, result, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "加载活动能量数据失败: \(error.localizedDescription)"
                    return
                }
                
                if let sum = result?.sumQuantity() {
                    let energy = sum.doubleValue(for: HKUnit.kilocalorie())
                    self?.activeEnergyBurned = energy
                }
            }
        }
        
        healthStore.execute(query)
    }
    
    // MARK: - Data Writing
    func saveFoodRecord(_ record: FoodRecord) {
        let nutrition = record.actualNutrition
        let startDate = record.timestamp
        let endDate = record.timestamp
        
        var samples: [HKQuantitySample] = []
        
        // 能量
        if let energySample = createNutritionSample(
            identifier: .dietaryEnergyConsumed,
            value: nutrition.energyKcal,
            unit: .kilocalorie(),
            startDate: startDate,
            endDate: endDate
        ) {
            samples.append(energySample)
        }
        
        // 蛋白质
        if let proteinSample = createNutritionSample(
            identifier: .dietaryProtein,
            value: nutrition.protein,
            unit: .gram(),
            startDate: startDate,
            endDate: endDate
        ) {
            samples.append(proteinSample)
        }
        
        // 脂肪
        if let fatSample = createNutritionSample(
            identifier: .dietaryFatTotal,
            value: nutrition.fat,
            unit: .gram(),
            startDate: startDate,
            endDate: endDate
        ) {
            samples.append(fatSample)
        }
        
        // 碳水化合物
        if let carbSample = createNutritionSample(
            identifier: .dietaryCarbohydrates,
            value: nutrition.carbohydrate,
            unit: .gram(),
            startDate: startDate,
            endDate: endDate
        ) {
            samples.append(carbSample)
        }
        
        // 钠
        if let sodiumSample = createNutritionSample(
            identifier: .dietarySodium,
            value: nutrition.sodium,
            unit: .gramUnit(with: .milli),
            startDate: startDate,
            endDate: endDate
        ) {
            samples.append(sodiumSample)
        }
        
        // 保存到 HealthKit
        healthStore.save(samples) { [weak self] success, error in
            DispatchQueue.main.async {
                if let error = error {
                    self?.errorMessage = "保存营养数据到 HealthKit 失败: \(error.localizedDescription)"
                } else if success {
                    print("营养数据已成功保存到 HealthKit")
                }
            }
        }
    }
    
    private func createNutritionSample(
        identifier: HKQuantityTypeIdentifier,
        value: Double,
        unit: HKUnit,
        startDate: Date,
        endDate: Date
    ) -> HKQuantitySample? {
        guard let quantityType = HKQuantityType.quantityType(forIdentifier: identifier) else {
            return nil
        }
        
        let quantity = HKQuantity(unit: unit, doubleValue: value)
        return HKQuantitySample(
            type: quantityType,
            quantity: quantity,
            start: startDate,
            end: endDate
        )
    }
}
