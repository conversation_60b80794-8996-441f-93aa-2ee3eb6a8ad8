import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    @EnvironmentObject var healthKitManager: HealthKitManager
    @State private var showingProfileEditor = false
    @State private var showingDataExport = false
    @State private var showingAbout = false
    
    var body: some View {
        NavigationView {
            List {
                // 用户资料部分
                Section {
                    UserProfileSection(showingProfileEditor: $showingProfileEditor)
                } header: {
                    Text("用户资料")
                }
                
                // 健康数据部分
                Section {
                    HealthDataSection()
                } header: {
                    Text("健康数据")
                }
                
                // 营养目标部分
                Section {
                    NutritionGoalsSection()
                } header: {
                    Text("营养目标")
                }
                
                // 数据管理部分
                Section {
                    DataManagementSection(showingDataExport: $showingDataExport)
                } header: {
                    Text("数据管理")
                }
                
                // 应用设置部分
                Section {
                    AppSettingsSection()
                } header: {
                    Text("应用设置")
                }
                
                // 关于部分
                Section {
                    AboutSection(showingAbout: $showingAbout)
                } header: {
                    Text("关于")
                }
            }
            .navigationTitle("设置")
            .sheet(isPresented: $showingProfileEditor) {
                UserProfileEditorView()
            }
            .sheet(isPresented: $showingDataExport) {
                DataExportView()
            }
            .sheet(isPresented: $showingAbout) {
                AboutView()
            }
        }
    }
}

struct UserProfileSection: View {
    @Binding var showingProfileEditor: Bool
    @EnvironmentObject var nutritionManager: NutritionManager
    @EnvironmentObject var healthKitManager: HealthKitManager
    
    var body: some View {
        if let profile = nutritionManager.userProfile {
            VStack(alignment: .leading, spacing: 8) {
                HStack {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(profile.name.isEmpty ? "未设置姓名" : profile.name)
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        Text("\(profile.gender.rawValue) • \(profile.age)岁")
                            .font(.subheadline)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("编辑") {
                        showingProfileEditor = true
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
                
                HStack(spacing: 20) {
                    ProfileMetric(
                        title: "身高",
                        value: "\(Int(profile.height))",
                        unit: "cm"
                    )
                    
                    ProfileMetric(
                        title: "体重",
                        value: String(format: "%.1f", profile.weight),
                        unit: "kg"
                    )
                    
                    ProfileMetric(
                        title: "BMI",
                        value: String(format: "%.1f", profile.bmi),
                        unit: profile.bmiCategory
                    )
                }
            }
            .padding(.vertical, 8)
        } else {
            Button(action: {
                showingProfileEditor = true
            }) {
                HStack {
                    Image(systemName: "person.circle")
                        .foregroundColor(.blue)
                    
                    Text("创建用户资料")
                        .foregroundColor(.blue)
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

struct ProfileMetric: View {
    let title: String
    let value: String
    let unit: String
    
    var body: some View {
        VStack(spacing: 2) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text(value)
                .font(.subheadline)
                .fontWeight(.semibold)
            
            Text(unit)
                .font(.caption2)
                .foregroundColor(.secondary)
        }
    }
}

struct HealthDataSection: View {
    @EnvironmentObject var healthKitManager: HealthKitManager
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Image(systemName: "heart.fill")
                    .foregroundColor(.red)
                
                Text("HealthKit 集成")
                
                Spacer()
                
                if healthKitManager.isAuthorized {
                    Image(systemName: "checkmark.circle.fill")
                        .foregroundColor(.green)
                } else {
                    Button("授权") {
                        healthKitManager.requestAuthorization { _ in }
                    }
                    .font(.caption)
                    .foregroundColor(.blue)
                }
            }
            
            if healthKitManager.isAuthorized {
                VStack(spacing: 8) {
                    if let steps = healthKitManager.stepCount {
                        HealthMetricRow(title: "今日步数", value: "\(steps)", unit: "步")
                    }
                    
                    if let energy = healthKitManager.activeEnergyBurned {
                        HealthMetricRow(title: "活动能量", value: "\(Int(energy))", unit: "kcal")
                    }
                }
            }
        }
    }
}

struct HealthMetricRow: View {
    let title: String
    let value: String
    let unit: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.subheadline)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text("\(value) \(unit)")
                .font(.subheadline)
                .fontWeight(.medium)
        }
    }
}

struct NutritionGoalsSection: View {
    var body: some View {
        NavigationLink(destination: NutritionGoalsView()) {
            HStack {
                Image(systemName: "target")
                    .foregroundColor(.green)
                
                Text("营养目标设置")
                
                Spacer()
            }
        }
        
        NavigationLink(destination: FoodPreferencesView()) {
            HStack {
                Image(systemName: "heart.text.square")
                    .foregroundColor(.pink)
                
                Text("饮食偏好")
                
                Spacer()
            }
        }
    }
}

struct DataManagementSection: View {
    @Binding var showingDataExport: Bool
    @State private var showingClearDataAlert = false
    
    var body: some View {
        Button(action: {
            showingDataExport = true
        }) {
            HStack {
                Image(systemName: "square.and.arrow.up")
                    .foregroundColor(.blue)
                
                Text("导出数据")
                    .foregroundColor(.primary)
                
                Spacer()
            }
        }
        
        Button(action: {
            showingClearDataAlert = true
        }) {
            HStack {
                Image(systemName: "trash")
                    .foregroundColor(.red)
                
                Text("清除所有数据")
                    .foregroundColor(.red)
                
                Spacer()
            }
        }
        .alert("清除数据", isPresented: $showingClearDataAlert) {
            Button("取消", role: .cancel) { }
            Button("确认清除", role: .destructive) {
                // 实现数据清除逻辑
            }
        } message: {
            Text("此操作将删除所有食物记录和用户数据，且无法恢复。确定要继续吗？")
        }
    }
}

struct AppSettingsSection: View {
    @AppStorage("notificationsEnabled") private var notificationsEnabled = true
    @AppStorage("reminderTimeHour") private var reminderTimeHour = 8
    @AppStorage("reminderTimeMinute") private var reminderTimeMinute = 0
    @AppStorage("darkModeEnabled") private var darkModeEnabled = false

    private var reminderTime: Date {
        let calendar = Calendar.current
        var components = DateComponents()
        components.hour = reminderTimeHour
        components.minute = reminderTimeMinute
        return calendar.date(from: components) ?? Date()
    }
    
    var body: some View {
        Toggle(isOn: $notificationsEnabled) {
            HStack {
                Image(systemName: "bell")
                    .foregroundColor(.orange)
                
                Text("推送通知")
            }
        }
        
        if notificationsEnabled {
            DatePicker(
                selection: Binding(
                    get: { reminderTime },
                    set: { newDate in
                        let calendar = Calendar.current
                        let components = calendar.dateComponents([.hour, .minute], from: newDate)
                        reminderTimeHour = components.hour ?? 8
                        reminderTimeMinute = components.minute ?? 0
                    }
                ),
                displayedComponents: .hourAndMinute
            ) {
                HStack {
                    Image(systemName: "clock")
                        .foregroundColor(.blue)

                    Text("提醒时间")
                }
            }
        }
        
        Toggle(isOn: $darkModeEnabled) {
            HStack {
                Image(systemName: "moon")
                    .foregroundColor(.purple)
                
                Text("深色模式")
            }
        }
    }
}

struct AboutSection: View {
    @Binding var showingAbout: Bool
    
    var body: some View {
        Button(action: {
            showingAbout = true
        }) {
            HStack {
                Image(systemName: "info.circle")
                    .foregroundColor(.blue)
                
                Text("关于应用")
                    .foregroundColor(.primary)
                
                Spacer()
                
                Text("v1.0.0")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        
        Link(destination: URL(string: "https://github.com/dom-liu/gbdietitian-ios-app")!) {
            HStack {
                Image(systemName: "link")
                    .foregroundColor(.blue)
                
                Text("GitHub 项目")
                    .foregroundColor(.primary)
                
                Spacer()
                
                Image(systemName: "arrow.up.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Placeholder Views (待实现)
struct UserProfileEditorView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("用户资料编辑")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                Text("即将实现...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("编辑资料")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct NutritionGoalsView: View {
    var body: some View {
        Text("营养目标设置")
            .navigationTitle("营养目标")
            .navigationBarTitleDisplayMode(.inline)
    }
}

struct FoodPreferencesView: View {
    var body: some View {
        Text("饮食偏好设置")
            .navigationTitle("饮食偏好")
            .navigationBarTitleDisplayMode(.inline)
    }
}

struct DataExportView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            VStack {
                Text("数据导出")
                    .font(.title2)
                    .foregroundColor(.secondary)
                
                Text("即将实现...")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .navigationTitle("导出数据")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

struct AboutView: View {
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    Image(systemName: "leaf.circle.fill")
                        .font(.system(size: 80))
                        .foregroundColor(.green)
                    
                    Text("GuoBiaoDietitian")
                        .font(.title)
                        .fontWeight(.bold)
                    
                    Text("基于中国国标的营养膳食管理应用")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.center)
                    
                    VStack(alignment: .leading, spacing: 12) {
                        Text("核心特性")
                            .font(.headline)
                            .fontWeight(.semibold)
                        
                        VStack(alignment: .leading, spacing: 8) {
                            Text("• 严格遵循 GB 28050-2011 营养标签通则")
                            Text("• 基于《中国居民膳食指南 2022》")
                            Text("• 本地 AI 处理，保护隐私")
                            Text("• HealthKit 深度集成")
                            Text("• 专业营养分析与建议")
                        }
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    }
                    .padding()
                    .background(Color(.secondarySystemBackground))
                    .cornerRadius(12)
                    
                    Text("版本 1.0.0")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .padding()
            }
            .navigationTitle("关于")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("关闭") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    SettingsView()
        .environmentObject(NutritionManager())
        .environmentObject(HealthKitManager())
}
