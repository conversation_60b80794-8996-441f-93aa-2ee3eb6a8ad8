import Foundation

/// 营养计算工具类
struct NutritionCalculator {
    
    // MARK: - 基础代谢率计算
    
    /// 使用 Mifflin-St Jeor 公式计算基础代谢率 (BMR)
    /// - Parameters:
    ///   - weight: 体重 (kg)
    ///   - height: 身高 (cm)
    ///   - age: 年龄
    ///   - gender: 性别
    /// - Returns: 基础代谢率 (kcal/day)
    static func calculateBMR(weight: Double, height: Double, age: Int, gender: UserProfile.Gender) -> Double {
        let baseValue = 10 * weight + 6.25 * height - 5 * Double(age)
        
        switch gender {
        case .male:
            return baseValue + 5
        case .female:
            return baseValue - 161
        }
    }
    
    /// 计算总日消耗能量 (TDEE)
    /// - Parameters:
    ///   - bmr: 基础代谢率
    ///   - activityLevel: 活动水平
    /// - Returns: 总日消耗能量 (kcal/day)
    static func calculateTDEE(bmr: Double, activityLevel: UserProfile.ActivityLevel) -> Double {
        let activityFactor: Double
        
        switch activityLevel {
        case .sedentary:
            activityFactor = 1.2
        case .lightlyActive:
            activityFactor = 1.375
        case .moderatelyActive:
            activityFactor = 1.55
        case .veryActive:
            activityFactor = 1.725
        case .extraActive:
            activityFactor = 1.9
        }
        
        return bmr * activityFactor
    }
    
    // MARK: - 营养需求计算
    
    /// 计算个性化营养需求
    /// - Parameter profile: 用户资料
    /// - Returns: 个性化营养推荐
    static func calculatePersonalizedRecommendation(for profile: UserProfile) -> ChineseDietaryStandards.DailyRecommendation {
        let baseRecommendation = ChineseDietaryStandards.DietaryRecommendations.dailyRecommendation(
            for: profile.gender,
            activityLevel: profile.activityLevel
        )
        
        // 年龄调整
        let ageAdjustment = ChineseDietaryStandards.SpecialPopulationFactors.ageAdjustmentFactor(age: profile.age)
        
        // BMI调整
        let bmiAdjustment = ChineseDietaryStandards.SpecialPopulationFactors.bmiAdjustmentFactor(bmi: profile.bmi)
        
        // 综合调整系数
        let adjustmentFactor = ageAdjustment * bmiAdjustment
        
        return ChineseDietaryStandards.DailyRecommendation(
            energy: baseRecommendation.energy * adjustmentFactor,
            protein: baseRecommendation.protein * adjustmentFactor,
            fat: baseRecommendation.fat * adjustmentFactor,
            carbohydrate: baseRecommendation.carbohydrate * adjustmentFactor
        )
    }
    
    // MARK: - 营养素分析
    
    /// 计算营养素密度
    /// - Parameters:
    ///   - nutrientAmount: 营养素含量
    ///   - energy: 能量含量 (kcal)
    ///   - nrv: 营养素参考值
    /// - Returns: 营养素密度等级
    static func calculateNutrientDensity(nutrientAmount: Double, energy: Double, nrv: Double) -> ChineseDietaryStandards.NutrientDensityLevel {
        return ChineseDietaryStandards.nutrientDensity(nutrient: nutrientAmount, energy: energy, nrv: nrv)
    }
    
    /// 评估营养素摄入状态
    /// - Parameters:
    ///   - intake: 实际摄入量
    ///   - recommendation: 推荐摄入量
    /// - Returns: 营养状态
    static func assessNutrientStatus(intake: Double, recommendation: Double) -> NutritionStatus {
        let ratio = intake / recommendation
        
        switch ratio {
        case 0..<0.5:
            return .deficient
        case 0.5..<0.8:
            return .insufficient
        case 0.8..<1.2:
            return .adequate
        case 1.2..<1.5:
            return .high
        default:
            return .excessive
        }
    }
    
    // MARK: - 膳食平衡分析
    
    /// 分析宏量营养素平衡
    /// - Parameter nutrition: 营养成分
    /// - Returns: 宏量营养素平衡分析
    static func analyzeMacronutrientBalance(_ nutrition: NutritionFacts) -> MacronutrientBalance {
        let totalEnergy = nutrition.energyKcal
        
        guard totalEnergy > 0 else {
            return MacronutrientBalance(
                proteinPercentage: 0,
                fatPercentage: 0,
                carbohydratePercentage: 0,
                isBalanced: false
            )
        }
        
        let proteinEnergy = nutrition.protein * 4 // 1g蛋白质 = 4kcal
        let fatEnergy = nutrition.fat * 9 // 1g脂肪 = 9kcal
        let carbohydrateEnergy = nutrition.carbohydrate * 4 // 1g碳水化合物 = 4kcal
        
        let proteinPercentage = (proteinEnergy / totalEnergy) * 100
        let fatPercentage = (fatEnergy / totalEnergy) * 100
        let carbohydratePercentage = (carbohydrateEnergy / totalEnergy) * 100
        
        // 中国膳食指南推荐比例：蛋白质10-15%，脂肪20-30%，碳水化合物50-65%
        let isBalanced = proteinPercentage >= 10 && proteinPercentage <= 15 &&
                        fatPercentage >= 20 && fatPercentage <= 30 &&
                        carbohydratePercentage >= 50 && carbohydratePercentage <= 65
        
        return MacronutrientBalance(
            proteinPercentage: proteinPercentage,
            fatPercentage: fatPercentage,
            carbohydratePercentage: carbohydratePercentage,
            isBalanced: isBalanced
        )
    }
    
    // MARK: - 食物组分析
    
    /// 分析食物组摄入情况
    /// - Parameter records: 食物记录
    /// - Returns: 食物组摄入分析
    static func analyzeFoodGroupIntake(_ records: [FoodRecord]) -> [String: Double] {
        var foodGroupIntake: [String: Double] = [:]
        
        for record in records {
            let category = record.food.category.rawValue
            let amount = record.amount
            
            foodGroupIntake[category, default: 0] += amount
        }
        
        return foodGroupIntake
    }
    
    /// 评估食物组摄入是否达标
    /// - Parameter foodGroupIntake: 食物组摄入量
    /// - Returns: 达标情况
    static func assessFoodGroupAdequacy(_ foodGroupIntake: [String: Double]) -> [String: Bool] {
        var adequacy: [String: Bool] = [:]
        
        for (foodGroup, intake) in foodGroupIntake {
            if let recommendedRange = ChineseDietaryStandards.DietaryRecommendations.foodGroupRecommendations[foodGroup] {
                adequacy[foodGroup] = recommendedRange.contains(intake)
            }
        }
        
        return adequacy
    }
    
    // MARK: - 餐次分析
    
    /// 分析餐次能量分布
    /// - Parameter records: 食物记录
    /// - Returns: 餐次能量分布
    static func analyzeMealEnergyDistribution(_ records: [FoodRecord]) -> [FoodRecord.MealType: Double] {
        var mealEnergy: [FoodRecord.MealType: Double] = [:]
        
        for record in records {
            let energy = record.actualNutrition.energyKcal
            mealEnergy[record.mealType, default: 0] += energy
        }
        
        return mealEnergy
    }
    
    /// 评估餐次分布是否合理
    /// - Parameter mealDistribution: 餐次能量分布
    /// - Returns: 分布是否合理
    static func assessMealDistribution(_ mealDistribution: [FoodRecord.MealType: Double]) -> Bool {
        let totalEnergy = mealDistribution.values.reduce(0, +)
        
        guard totalEnergy > 0 else { return false }
        
        let recommendedDistribution = ChineseDietaryStandards.DietaryRecommendations.mealEnergyDistribution
        
        for (mealType, energy) in mealDistribution {
            let actualPercentage = energy / totalEnergy
            let recommendedPercentage = recommendedDistribution[mealType] ?? 0
            
            // 允许±10%的偏差
            let tolerance = 0.1
            if abs(actualPercentage - recommendedPercentage) > tolerance {
                return false
            }
        }
        
        return true
    }
    
    // MARK: - 营养风险评估
    
    /// 评估营养风险
    /// - Parameters:
    ///   - nutrition: 营养摄入
    ///   - profile: 用户资料
    /// - Returns: 营养风险列表
    static func assessNutritionRisks(nutrition: NutritionFacts, profile: UserProfile) -> [NutritionRisk] {
        var risks: [NutritionRisk] = []
        
        // 钠摄入过量风险
        if nutrition.sodium > 2300 {
            risks.append(.excessiveSodium)
        }
        
        // 饱和脂肪过量风险
        if let saturatedFat = nutrition.saturatedFat, saturatedFat > 20 {
            risks.append(.excessiveSaturatedFat)
        }
        
        // 反式脂肪风险
        if let transFat = nutrition.transFat, transFat > 0 {
            risks.append(.transFatPresent)
        }
        
        // 膳食纤维不足风险
        if let fiber = nutrition.dietaryFiber, fiber < 25 {
            risks.append(.insufficientFiber)
        }
        
        // 蛋白质不足风险 (特别是老年人)
        if profile.age >= 65 && nutrition.protein < 1.2 * profile.weight {
            risks.append(.insufficientProtein)
        }
        
        return risks
    }
}

// MARK: - 营养风险枚举
enum NutritionRisk: String, CaseIterable {
    case excessiveSodium = "钠摄入过量"
    case excessiveSaturatedFat = "饱和脂肪过量"
    case transFatPresent = "含有反式脂肪"
    case insufficientFiber = "膳食纤维不足"
    case insufficientProtein = "蛋白质不足"
    case excessiveEnergy = "能量摄入过量"
    case insufficientCalcium = "钙摄入不足"
    case insufficientIron = "铁摄入不足"
    
    var description: String {
        switch self {
        case .excessiveSodium:
            return "钠摄入超过推荐量，可能增加高血压风险"
        case .excessiveSaturatedFat:
            return "饱和脂肪摄入过多，可能影响心血管健康"
        case .transFatPresent:
            return "检测到反式脂肪，建议避免摄入"
        case .insufficientFiber:
            return "膳食纤维摄入不足，可能影响肠道健康"
        case .insufficientProtein:
            return "蛋白质摄入不足，可能影响肌肉健康"
        case .excessiveEnergy:
            return "能量摄入过多，可能导致体重增加"
        case .insufficientCalcium:
            return "钙摄入不足，可能影响骨骼健康"
        case .insufficientIron:
            return "铁摄入不足，可能导致贫血"
        }
    }
    
    var severity: RiskSeverity {
        switch self {
        case .transFatPresent, .excessiveSodium:
            return .high
        case .excessiveSaturatedFat, .insufficientProtein:
            return .medium
        case .insufficientFiber, .excessiveEnergy, .insufficientCalcium, .insufficientIron:
            return .low
        }
    }
}

enum RiskSeverity: String {
    case low = "低"
    case medium = "中"
    case high = "高"
}
