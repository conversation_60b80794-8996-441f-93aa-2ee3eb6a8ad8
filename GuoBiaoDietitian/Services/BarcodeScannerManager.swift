@preconcurrency import AVFoundation
import SwiftUI
import Combine
import AudioToolbox

@MainActor
class BarcodeScannerManager: NSObject, ObservableObject {
    @Published var isScanning = false
    @Published var scannedCode: String?
    @Published var errorMessage: String?
    @Published var hasPermission = false
    
    private var captureSession: AVCaptureSession?
    private var previewLayer: AVCaptureVideoPreviewLayer?
    private var cancellables = Set<AnyCancellable>()
    
    override init() {
        super.init()
        checkCameraPermission()
    }
    
    func checkCameraPermission() {
        switch AVCaptureDevice.authorizationStatus(for: .video) {
        case .authorized:
            hasPermission = true
        case .notDetermined:
            requestCameraPermission()
        case .denied, .restricted:
            hasPermission = false
            errorMessage = "需要相机权限来扫描条码"
        @unknown default:
            hasPermission = false
        }
    }
    
    private func requestCameraPermission() {
        AVCaptureDevice.requestAccess(for: .video) { [weak self] granted in
            DispatchQueue.main.async {
                self?.hasPermission = granted
                if !granted {
                    self?.errorMessage = "需要相机权限来扫描条码"
                }
            }
        }
    }
    
    func startScanning() {
        guard hasPermission else {
            errorMessage = "没有相机权限"
            return
        }
        
        guard !isScanning else { return }
        
        setupCaptureSession()
        isScanning = true
        scannedCode = nil
        errorMessage = nil
    }
    
    func stopScanning() {
        captureSession?.stopRunning()
        isScanning = false
    }
    
    private func setupCaptureSession() {
        captureSession = AVCaptureSession()
        
        guard let captureSession = captureSession else {
            errorMessage = "无法创建扫描会话"
            return
        }
        
        guard let videoCaptureDevice = AVCaptureDevice.default(for: .video) else {
            errorMessage = "无法访问相机"
            return
        }
        
        do {
            let videoInput = try AVCaptureDeviceInput(device: videoCaptureDevice)
            
            if captureSession.canAddInput(videoInput) {
                captureSession.addInput(videoInput)
            } else {
                errorMessage = "无法添加视频输入"
                return
            }
            
            let metadataOutput = AVCaptureMetadataOutput()
            
            if captureSession.canAddOutput(metadataOutput) {
                captureSession.addOutput(metadataOutput)
                
                metadataOutput.setMetadataObjectsDelegate(self, queue: DispatchQueue.main)
                metadataOutput.metadataObjectTypes = [
                    .ean8,
                    .ean13,
                    .pdf417,
                    .qr,
                    .code128,
                    .code39,
                    .code93,
                    .upce
                ]
            } else {
                errorMessage = "无法添加元数据输出"
                return
            }
            
            DispatchQueue.global(qos: .userInitiated).async { [captureSession] in
                captureSession.startRunning()
            }
            
        } catch {
            errorMessage = "设置相机失败: \(error.localizedDescription)"
        }
    }
    
    func getPreviewLayer() -> AVCaptureVideoPreviewLayer? {
        guard let captureSession = captureSession else { return nil }
        
        if previewLayer == nil {
            previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
            previewLayer?.videoGravity = .resizeAspectFill
        }
        
        return previewLayer
    }
    
    func resetScanner() {
        scannedCode = nil
        errorMessage = nil
    }
}

// MARK: - AVCaptureMetadataOutputObjectsDelegate
extension BarcodeScannerManager: AVCaptureMetadataOutputObjectsDelegate {
    nonisolated func metadataOutput(_ output: AVCaptureMetadataOutput, didOutput metadataObjects: [AVMetadataObject], from connection: AVCaptureConnection) {

        guard let metadataObject = metadataObjects.first else { return }
        guard let readableObject = metadataObject as? AVMetadataMachineReadableCodeObject else { return }
        guard let stringValue = readableObject.stringValue else { return }

        Task { @MainActor in
            // 避免重复扫描同一个条码
            guard scannedCode != stringValue else { return }

            // 播放扫描成功音效
            AudioServicesPlaySystemSound(SystemSoundID(kSystemSoundID_Vibrate))

            scannedCode = stringValue
            stopScanning()
        }
    }
}

// MARK: - Camera Preview UIViewRepresentable
struct CameraPreview: UIViewRepresentable {
    let previewLayer: AVCaptureVideoPreviewLayer
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.layer.addSublayer(previewLayer)
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        DispatchQueue.main.async {
            previewLayer.frame = uiView.bounds
        }
    }
}

// MARK: - Barcode Scanner View
struct BarcodeScannerView: View {
    let selectedMealType: FoodRecord.MealType
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var nutritionManager: NutritionManager
    
    @StateObject private var scannerManager = BarcodeScannerManager()
    @State private var showingFoodDetail = false
    @State private var foundFood: Food?
    @State private var isSearching = false
    
    private let foodRepository = FoodRepository()
    
    var body: some View {
        NavigationView {
            ZStack {
                // 相机预览
                if scannerManager.hasPermission, let previewLayer = scannerManager.getPreviewLayer() {
                    CameraPreview(previewLayer: previewLayer)
                        .ignoresSafeArea()
                } else {
                    // 权限请求或错误状态
                    PermissionRequestView(scannerManager: scannerManager)
                }
                
                // 扫描框和指导
                ScannerOverlayView()
                
                // 扫描结果处理
                if isSearching {
                    SearchingOverlayView()
                }
                
                if let errorMessage = scannerManager.errorMessage {
                    ErrorOverlayView(message: errorMessage) {
                        scannerManager.resetScanner()
                    }
                }
            }
            .navigationTitle("扫描条码")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                    .foregroundColor(.white)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("手电筒") {
                        toggleTorch()
                    }
                    .foregroundColor(.white)
                }
            }
            .onAppear {
                scannerManager.startScanning()
            }
            .onDisappear {
                scannerManager.stopScanning()
            }
            .onChange(of: scannerManager.scannedCode) {
                if let code = scannerManager.scannedCode {
                    searchFoodByBarcode(code)
                }
            }
            .sheet(isPresented: $showingFoodDetail) {
                if let food = foundFood {
                    FoodDetailView(
                        food: food,
                        mealType: selectedMealType,
                        onAddFood: { record in
                            nutritionManager.addFoodRecord(record)
                            dismiss()
                        }
                    )
                }
            }
        }
    }
    
    private func searchFoodByBarcode(_ barcode: String) {
        isSearching = true
        
        // 模拟网络搜索延迟
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            if let food = foodRepository.getFoodByBarcode(barcode) {
                foundFood = food
                showingFoodDetail = true
            } else {
                // 未找到食物，显示错误信息
                scannerManager.errorMessage = "未找到条码对应的食物信息"
                scannerManager.resetScanner()
                scannerManager.startScanning()
            }
            isSearching = false
        }
    }
    
    private func toggleTorch() {
        guard let device = AVCaptureDevice.default(for: .video),
              device.hasTorch else { return }
        
        do {
            try device.lockForConfiguration()
            device.torchMode = device.isTorchActive ? .off : .on
            device.unlockForConfiguration()
        } catch {
            print("手电筒控制失败: \(error)")
        }
    }
}

struct PermissionRequestView: View {
    let scannerManager: BarcodeScannerManager
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "camera.fill")
                .font(.system(size: 60))
                .foregroundColor(.gray)
            
            Text("需要相机权限")
                .font(.title2)
                .fontWeight(.semibold)
            
            Text("请允许访问相机以扫描食物条码")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
            
            Button("请求权限") {
                scannerManager.checkCameraPermission()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
    }
}

struct ScannerOverlayView: View {
    var body: some View {
        VStack {
            Spacer()
            
            // 扫描框
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.white, lineWidth: 3)
                .frame(width: 250, height: 150)
                .overlay(
                    // 扫描线动画
                    Rectangle()
                        .fill(Color.green)
                        .frame(height: 2)
                        .offset(y: -75)
                        .animation(
                            Animation.easeInOut(duration: 1.5)
                                .repeatForever(autoreverses: true),
                            value: UUID()
                        )
                )
            
            Text("将条码对准扫描框")
                .font(.headline)
                .foregroundColor(.white)
                .padding(.top, 20)
            
            Text("支持 EAN-13、EAN-8、Code 128 等格式")
                .font(.caption)
                .foregroundColor(.white.opacity(0.8))
                .padding(.top, 8)
            
            Spacer()
        }
    }
}

struct SearchingOverlayView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .white))
                .scaleEffect(1.5)
            
            Text("正在查找食物信息...")
                .font(.headline)
                .foregroundColor(.white)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.black.opacity(0.7))
    }
}

struct ErrorOverlayView: View {
    let message: String
    let onDismiss: () -> Void
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 40))
                .foregroundColor(.yellow)
            
            Text(message)
                .font(.headline)
                .foregroundColor(.white)
                .multilineTextAlignment(.center)
            
            Button("重新扫描") {
                onDismiss()
            }
            .buttonStyle(.borderedProminent)
        }
        .padding()
        .background(Color.black.opacity(0.8))
        .cornerRadius(12)
        .padding()
    }
}

#Preview {
    BarcodeScannerView(selectedMealType: .lunch)
        .environmentObject(NutritionManager())
}
