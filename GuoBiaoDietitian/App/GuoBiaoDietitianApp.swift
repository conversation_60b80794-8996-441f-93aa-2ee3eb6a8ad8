import SwiftUI
import HealthKit

@main
struct GuoBiaoDietitianApp: App {
    @StateObject private var healthKitManager = HealthKitManager()
    @StateObject private var nutritionManager = NutritionManager()
    
    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(healthKitManager)
                .environmentObject(nutritionManager)
                .onAppear {
                    requestHealthKitPermissions()
                }
        }
    }
    
    private func requestHealthKitPermissions() {
        healthKitManager.requestAuthorization { success in
            if success {
                print("HealthKit authorization granted")
            } else {
                print("HealthKit authorization denied")
            }
        }
    }
}
