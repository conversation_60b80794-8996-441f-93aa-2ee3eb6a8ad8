import SwiftUI

struct ContentView: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // 主页 - 营养概览
            NutritionOverviewView()
                .tabItem {
                    Image(systemName: "chart.pie.fill")
                    Text("营养概览")
                }
                .tag(0)
            
            // 食物记录
            FoodRecordView()
                .tabItem {
                    Image(systemName: "plus.circle.fill")
                    Text("记录食物")
                }
                .tag(1)
            
            // 趋势分析
            TrendAnalysisView()
                .tabItem {
                    Image(systemName: "chart.line.uptrend.xyaxis")
                    Text("趋势分析")
                }
                .tag(2)
            
            // AI 助手
            AIAssistantView()
                .tabItem {
                    Image(systemName: "brain.head.profile")
                    Text("AI 助手")
                }
                .tag(3)
            
            // 设置
            SettingsView()
                .tabItem {
                    Image(systemName: "gearshape.fill")
                    Text("设置")
                }
                .tag(4)
        }
        .accentColor(.green)
    }
}

#Preview {
    ContentView()
        .environmentObject(NutritionManager())
        .environmentObject(HealthKitManager())
}
