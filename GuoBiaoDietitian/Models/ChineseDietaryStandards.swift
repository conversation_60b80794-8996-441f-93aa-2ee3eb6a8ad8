import Foundation

// MARK: - 中国膳食标准 (基于GB 28050-2011和《中国居民膳食指南2022》)
struct ChineseDietaryStandards {
    
    // MARK: - 营养素参考值 (NRV) - GB 28050-2011 附录A
    static let nrvValues: [String: Double] = [
        // 能量和宏量营养素
        "energy": 8400, // kJ (2000 kcal)
        "protein": 60, // g
        "fat": 60, // g
        "carbohydrate": 300, // g
        "sodium": 2000, // mg
        "dietaryFiber": 25, // g
        
        // 维生素
        "vitaminA": 800, // μg RE
        "vitaminD": 5, // μg
        "vitaminE": 14, // mg α-TE
        "vitaminK": 80, // μg
        "vitaminC": 100, // mg
        "thiamine": 1.4, // mg (维生素B1)
        "riboflavin": 1.4, // mg (维生素B2)
        "niacin": 14, // mg
        "vitaminB6": 1.4, // mg
        "folate": 400, // μg
        "vitaminB12": 2.4, // μg
        "biotin": 30, // μg
        "pantothenicAcid": 5, // mg
        
        // 矿物质
        "calcium": 800, // mg
        "phosphorus": 700, // mg
        "potassium": 2000, // mg
        "chloride": 2300, // mg
        "magnesium": 300, // mg
        "iron": 15, // mg
        "zinc": 11.5, // mg
        "iodine": 150, // μg
        "selenium": 50, // μg
        "copper": 0.8, // mg
        "fluoride": 1.0, // mg
        "chromium": 30, // μg
        "molybdenum": 60 // μg
    ]
    
    // MARK: - 中国居民膳食指南2022推荐摄入量
    struct DietaryRecommendations {
        
        // 成年人每日推荐摄入量 (18-49岁)
        static func dailyRecommendation(for gender: UserProfile.Gender, activityLevel: UserProfile.ActivityLevel) -> DailyRecommendation {
            switch (gender, activityLevel) {
            case (.male, .sedentary):
                return DailyRecommendation(energy: 2250, protein: 65, fat: 75, carbohydrate: 338)
            case (.male, .lightlyActive):
                return DailyRecommendation(energy: 2550, protein: 70, fat: 85, carbohydrate: 383)
            case (.male, .moderatelyActive):
                return DailyRecommendation(energy: 2850, protein: 75, fat: 95, carbohydrate: 428)
            case (.male, .veryActive):
                return DailyRecommendation(energy: 3150, protein: 80, fat: 105, carbohydrate: 473)
            case (.male, .extraActive):
                return DailyRecommendation(energy: 3450, protein: 85, fat: 115, carbohydrate: 518)
            case (.female, .sedentary):
                return DailyRecommendation(energy: 1800, protein: 55, fat: 60, carbohydrate: 270)
            case (.female, .lightlyActive):
                return DailyRecommendation(energy: 2050, protein: 60, fat: 68, carbohydrate: 308)
            case (.female, .moderatelyActive):
                return DailyRecommendation(energy: 2300, protein: 65, fat: 77, carbohydrate: 345)
            case (.female, .veryActive):
                return DailyRecommendation(energy: 2550, protein: 70, fat: 85, carbohydrate: 383)
            case (.female, .extraActive):
                return DailyRecommendation(energy: 2800, protein: 75, fat: 93, carbohydrate: 420)
            }
        }
        
        // 食物组推荐量 (g/天)
        static let foodGroupRecommendations: [String: ClosedRange<Double>] = [
            "谷薯类": 250...400,
            "蔬菜类": 300...500,
            "水果类": 200...350,
            "畜禽肉类": 120...200,
            "水产类": 40...75,
            "蛋类": 40...50,
            "奶类": 300...500,
            "大豆坚果类": 25...35,
            "烹调油": 25...30,
            "盐": 0...6 // 限制摄入
        ]
        
        // 三餐能量分配比例
        static let mealEnergyDistribution: [FoodRecord.MealType: Double] = [
            .breakfast: 0.25, // 25%
            .lunch: 0.40,     // 40%
            .dinner: 0.30,    // 30%
            .snack: 0.05      // 5%
        ]
    }
    
    // MARK: - 每日推荐摄入量
    struct DailyRecommendation {
        let energy: Double // kcal
        let protein: Double // g
        let fat: Double // g
        let carbohydrate: Double // g
        
        // 计算宏量营养素能量占比
        var proteinEnergyPercentage: Double {
            return (protein * 4) / energy * 100
        }
        
        var fatEnergyPercentage: Double {
            return (fat * 9) / energy * 100
        }
        
        var carbohydrateEnergyPercentage: Double {
            return (carbohydrate * 4) / energy * 100
        }
    }
    
    // MARK: - 营养素上限值 (UL - Tolerable Upper Intake Level)
    static let upperLimits: [String: Double] = [
        "vitaminA": 3000, // μg RE
        "vitaminD": 50, // μg
        "vitaminE": 300, // mg
        "niacin": 35, // mg
        "vitaminB6": 100, // mg
        "folate": 1000, // μg
        "vitaminC": 2000, // mg
        "calcium": 2000, // mg
        "phosphorus": 3500, // mg
        "magnesium": 350, // mg (来自补充剂)
        "iron": 42, // mg
        "zinc": 40, // mg
        "iodine": 600, // μg
        "selenium": 400, // μg
        "copper": 8, // mg
        "fluoride": 3.5, // mg
        "sodium": 2300 // mg (WHO推荐)
    ]
    
    // MARK: - 特殊人群调整系数
    struct SpecialPopulationFactors {
        // 年龄调整系数
        static func ageAdjustmentFactor(age: Int) -> Double {
            switch age {
            case 50...64:
                return 0.95
            case 65...79:
                return 0.90
            case 80...:
                return 0.85
            default:
                return 1.0
            }
        }
        
        // BMI调整系数
        static func bmiAdjustmentFactor(bmi: Double) -> Double {
            switch bmi {
            case ..<18.5:
                return 1.1 // 偏瘦需要增加摄入
            case 24..<28:
                return 0.9 // 超重需要减少摄入
            case 28...:
                return 0.8 // 肥胖需要明显减少摄入
            default:
                return 1.0 // 正常体重
            }
        }
    }
    
    // MARK: - 营养素密度评估
    static func nutrientDensity(nutrient: Double, energy: Double, nrv: Double) -> NutrientDensityLevel {
        let density = (nutrient / nrv) / (energy / 2000) // 基于2000kcal标准
        
        switch density {
        case 1.5...:
            return .excellent
        case 1.0..<1.5:
            return .good
        case 0.5..<1.0:
            return .adequate
        case 0.2..<0.5:
            return .low
        default:
            return .veryLow
        }
    }
    
    enum NutrientDensityLevel: String, CaseIterable {
        case excellent = "优秀"
        case good = "良好"
        case adequate = "充足"
        case low = "偏低"
        case veryLow = "很低"
        
        var color: String {
            switch self {
            case .excellent: return "green"
            case .good: return "lightGreen"
            case .adequate: return "yellow"
            case .low: return "orange"
            case .veryLow: return "red"
            }
        }
    }
}
