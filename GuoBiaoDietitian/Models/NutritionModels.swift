import Foundation

// MARK: - 营养素模型 (基于 GB 28050-2011)
struct Nutrient: Codable, Identifiable {
    let id: UUID
    let name: String
    let unit: String
    let nrv: Double? // 营养素参考值 (Nutrient Reference Value)
    let category: NutrientCategory

    init(name: String, unit: String, nrv: Double?, category: NutrientCategory) {
        self.id = UUID()
        self.name = name
        self.unit = unit
        self.nrv = nrv
        self.category = category
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decodeIfPresent(UUID.self, forKey: .id) ?? UUID()
        self.name = try container.decode(String.self, forKey: .name)
        self.unit = try container.decode(String.self, forKey: .unit)
        self.nrv = try container.decodeIfPresent(Double.self, forKey: .nrv)
        self.category = try container.decode(NutrientCategory.self, forKey: .category)
    }
    
    enum NutrientCategory: String, CaseIterable, Codable {
        case energy = "能量"
        case macronutrient = "宏量营养素"
        case vitamin = "维生素"
        case mineral = "矿物质"
        case other = "其他"
    }
}

// MARK: - 营养成分表 (符合 GB 28050)
struct NutritionFacts: Codable {
    // 强制标示营养成分 (GB 28050 第4.1条)
    let energy: Double // 能量 (kJ)
    let protein: Double // 蛋白质 (g)
    let fat: Double // 脂肪 (g)
    let carbohydrate: Double // 碳水化合物 (g)
    let sodium: Double // 钠 (mg)
    
    // 可选标示营养成分
    let saturatedFat: Double? // 饱和脂肪 (g)
    let transFat: Double? // 反式脂肪 (g)
    let cholesterol: Double? // 胆固醇 (mg)
    let dietaryFiber: Double? // 膳食纤维 (g)
    let sugar: Double? // 糖 (g)
    
    // 维生素
    let vitaminA: Double? // 维生素A (μg RE)
    let vitaminD: Double? // 维生素D (μg)
    let vitaminE: Double? // 维生素E (mg α-TE)
    let vitaminC: Double? // 维生素C (mg)
    let thiamine: Double? // 硫胺素(维生素B1) (mg)
    let riboflavin: Double? // 核黄素(维生素B2) (mg)
    let niacin: Double? // 烟酸 (mg)
    let vitaminB6: Double? // 维生素B6 (mg)
    let folate: Double? // 叶酸 (μg)
    let vitaminB12: Double? // 维生素B12 (μg)
    
    // 矿物质
    let calcium: Double? // 钙 (mg)
    let iron: Double? // 铁 (mg)
    let phosphorus: Double? // 磷 (mg)
    let potassium: Double? // 钾 (mg)
    let magnesium: Double? // 镁 (mg)
    let zinc: Double? // 锌 (mg)
    let selenium: Double? // 硒 (μg)
    let copper: Double? // 铜 (mg)
    let manganese: Double? // 锰 (mg)
    let iodine: Double? // 碘 (μg)
    
    // 计算能量 (kcal)
    var energyKcal: Double {
        return energy / 4.184 // 1 kcal = 4.184 kJ
    }
    
    // 计算营养素参考值百分比 (%NRV)
    func nrvPercentage(for nutrient: String, amount: Double) -> Double? {
        guard let nrv = ChineseDietaryStandards.nrvValues[nutrient] else { return nil }
        return (amount / nrv) * 100
    }
}

// MARK: - 食物模型
struct Food: Codable, Identifiable {
    let id: UUID
    let name: String
    let brand: String?
    let barcode: String?
    let category: FoodCategory
    let nutritionFacts: NutritionFacts // 每100g的营养成分
    let servingSize: Double? // 建议食用量 (g)
    let imageURL: String?
    let source: FoodSource
    let createdAt: Date
    let updatedAt: Date

    init(name: String, brand: String?, barcode: String?, category: FoodCategory, nutritionFacts: NutritionFacts, servingSize: Double?, imageURL: String?, source: FoodSource, createdAt: Date, updatedAt: Date) {
        self.id = UUID()
        self.name = name
        self.brand = brand
        self.barcode = barcode
        self.category = category
        self.nutritionFacts = nutritionFacts
        self.servingSize = servingSize
        self.imageURL = imageURL
        self.source = source
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decodeIfPresent(UUID.self, forKey: .id) ?? UUID()
        self.name = try container.decode(String.self, forKey: .name)
        self.brand = try container.decodeIfPresent(String.self, forKey: .brand)
        self.barcode = try container.decodeIfPresent(String.self, forKey: .barcode)
        self.category = try container.decode(FoodCategory.self, forKey: .category)
        self.nutritionFacts = try container.decode(NutritionFacts.self, forKey: .nutritionFacts)
        self.servingSize = try container.decodeIfPresent(Double.self, forKey: .servingSize)
        self.imageURL = try container.decodeIfPresent(String.self, forKey: .imageURL)
        self.source = try container.decode(FoodSource.self, forKey: .source)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
        self.updatedAt = try container.decode(Date.self, forKey: .updatedAt)
    }
    
    enum FoodCategory: String, CaseIterable, Codable {
        case grains = "谷薯类"
        case vegetables = "蔬菜水果类"
        case meat = "畜禽肉类"
        case seafood = "水产类"
        case eggs = "蛋类"
        case dairy = "奶类"
        case nuts = "坚果类"
        case oils = "油脂类"
        case beverages = "饮料类"
        case snacks = "零食类"
        case other = "其他"
    }
    
    enum FoodSource: String, Codable {
        case database = "数据库"
        case userInput = "用户输入"
        case barcodeScanned = "条码扫描"
        case photoRecognition = "拍照识别"
    }
}

// MARK: - 食物记录
struct FoodRecord: Codable, Identifiable {
    let id: UUID
    let food: Food
    let amount: Double // 实际食用量 (g)
    let mealType: MealType
    let timestamp: Date
    let notes: String?

    init(food: Food, amount: Double, mealType: MealType, timestamp: Date, notes: String?) {
        self.id = UUID()
        self.food = food
        self.amount = amount
        self.mealType = mealType
        self.timestamp = timestamp
        self.notes = notes
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decodeIfPresent(UUID.self, forKey: .id) ?? UUID()
        self.food = try container.decode(Food.self, forKey: .food)
        self.amount = try container.decode(Double.self, forKey: .amount)
        self.mealType = try container.decode(MealType.self, forKey: .mealType)
        self.timestamp = try container.decode(Date.self, forKey: .timestamp)
        self.notes = try container.decodeIfPresent(String.self, forKey: .notes)
    }
    
    enum MealType: String, CaseIterable, Codable {
        case breakfast = "早餐"
        case lunch = "午餐"
        case dinner = "晚餐"
        case snack = "加餐"
    }
    
    // 计算实际营养摄入
    var actualNutrition: NutritionFacts {
        let ratio = amount / 100.0 // 营养成分表基于100g
        return NutritionFacts(
            energy: food.nutritionFacts.energy * ratio,
            protein: food.nutritionFacts.protein * ratio,
            fat: food.nutritionFacts.fat * ratio,
            carbohydrate: food.nutritionFacts.carbohydrate * ratio,
            sodium: food.nutritionFacts.sodium * ratio,
            saturatedFat: food.nutritionFacts.saturatedFat.map { $0 * ratio },
            transFat: food.nutritionFacts.transFat.map { $0 * ratio },
            cholesterol: food.nutritionFacts.cholesterol.map { $0 * ratio },
            dietaryFiber: food.nutritionFacts.dietaryFiber.map { $0 * ratio },
            sugar: food.nutritionFacts.sugar.map { $0 * ratio },
            vitaminA: food.nutritionFacts.vitaminA.map { $0 * ratio },
            vitaminD: food.nutritionFacts.vitaminD.map { $0 * ratio },
            vitaminE: food.nutritionFacts.vitaminE.map { $0 * ratio },
            vitaminC: food.nutritionFacts.vitaminC.map { $0 * ratio },
            thiamine: food.nutritionFacts.thiamine.map { $0 * ratio },
            riboflavin: food.nutritionFacts.riboflavin.map { $0 * ratio },
            niacin: food.nutritionFacts.niacin.map { $0 * ratio },
            vitaminB6: food.nutritionFacts.vitaminB6.map { $0 * ratio },
            folate: food.nutritionFacts.folate.map { $0 * ratio },
            vitaminB12: food.nutritionFacts.vitaminB12.map { $0 * ratio },
            calcium: food.nutritionFacts.calcium.map { $0 * ratio },
            iron: food.nutritionFacts.iron.map { $0 * ratio },
            phosphorus: food.nutritionFacts.phosphorus.map { $0 * ratio },
            potassium: food.nutritionFacts.potassium.map { $0 * ratio },
            magnesium: food.nutritionFacts.magnesium.map { $0 * ratio },
            zinc: food.nutritionFacts.zinc.map { $0 * ratio },
            selenium: food.nutritionFacts.selenium.map { $0 * ratio },
            copper: food.nutritionFacts.copper.map { $0 * ratio },
            manganese: food.nutritionFacts.manganese.map { $0 * ratio },
            iodine: food.nutritionFacts.iodine.map { $0 * ratio }
        )
    }
}

// MARK: - 用户资料
struct UserProfile: Codable {
    let id: UUID
    var name: String
    var gender: Gender
    var birthDate: Date
    var height: Double // cm
    var weight: Double // kg
    var activityLevel: ActivityLevel
    var healthGoals: [HealthGoal]
    var medicalConditions: [String]
    var allergies: [String]
    var createdAt: Date
    var updatedAt: Date

    init(name: String, gender: Gender, birthDate: Date, height: Double, weight: Double, activityLevel: ActivityLevel, healthGoals: [HealthGoal], medicalConditions: [String], allergies: [String], createdAt: Date, updatedAt: Date) {
        self.id = UUID()
        self.name = name
        self.gender = gender
        self.birthDate = birthDate
        self.height = height
        self.weight = weight
        self.activityLevel = activityLevel
        self.healthGoals = healthGoals
        self.medicalConditions = medicalConditions
        self.allergies = allergies
        self.createdAt = createdAt
        self.updatedAt = updatedAt
    }

    init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.id = try container.decodeIfPresent(UUID.self, forKey: .id) ?? UUID()
        self.name = try container.decode(String.self, forKey: .name)
        self.gender = try container.decode(Gender.self, forKey: .gender)
        self.birthDate = try container.decode(Date.self, forKey: .birthDate)
        self.height = try container.decode(Double.self, forKey: .height)
        self.weight = try container.decode(Double.self, forKey: .weight)
        self.activityLevel = try container.decode(ActivityLevel.self, forKey: .activityLevel)
        self.healthGoals = try container.decode([HealthGoal].self, forKey: .healthGoals)
        self.medicalConditions = try container.decode([String].self, forKey: .medicalConditions)
        self.allergies = try container.decode([String].self, forKey: .allergies)
        self.createdAt = try container.decode(Date.self, forKey: .createdAt)
        self.updatedAt = try container.decode(Date.self, forKey: .updatedAt)
    }
    
    enum Gender: String, CaseIterable, Codable {
        case male = "男"
        case female = "女"
    }
    
    enum ActivityLevel: String, CaseIterable, Codable {
        case sedentary = "久坐少动"
        case lightlyActive = "轻度活动"
        case moderatelyActive = "中度活动"
        case veryActive = "重度活动"
        case extraActive = "极重度活动"
    }
    
    enum HealthGoal: String, CaseIterable, Codable {
        case weightLoss = "减重"
        case weightGain = "增重"
        case muscleGain = "增肌"
        case maintenance = "维持"
        case healthImprovement = "改善健康"
    }
    
    // 计算年龄
    var age: Int {
        Calendar.current.dateComponents([.year], from: birthDate, to: Date()).year ?? 0
    }
    
    // 计算BMI
    var bmi: Double {
        let heightInMeters = height / 100.0
        return weight / (heightInMeters * heightInMeters)
    }
    
    // BMI分类 (基于中国标准)
    var bmiCategory: String {
        switch bmi {
        case ..<18.5:
            return "偏瘦"
        case 18.5..<24:
            return "正常"
        case 24..<28:
            return "超重"
        default:
            return "肥胖"
        }
    }
}
