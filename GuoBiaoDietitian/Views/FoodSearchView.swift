import SwiftUI

struct FoodSearchView: View {
    let selectedMealType: FoodRecord.MealType
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var nutritionManager: NutritionManager
    
    @State private var searchText = ""
    @State private var selectedCategory: Food.FoodCategory? = nil
    @State private var searchResults: [Food] = []
    @State private var isLoading = false
    @State private var showingFoodDetail = false
    @State private var selectedFood: Food?
    
    private let foodRepository = FoodRepository()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 搜索栏
                SearchBar(searchText: $searchText, onSearchChanged: performSearch)
                
                // 分类筛选
                CategoryFilterView(selectedCategory: $selectedCategory, onCategoryChanged: performSearch)
                
                // 搜索结果
                if isLoading {
                    LoadingView()
                } else if searchResults.isEmpty && !searchText.isEmpty {
                    EmptySearchResultsView()
                } else {
                    FoodSearchResultsList(
                        foods: searchResults,
                        onFoodSelected: { food in
                            selectedFood = food
                            showingFoodDetail = true
                        }
                    )
                }
                
                Spacer()
            }
            .navigationTitle("搜索食物")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
            }
            .onAppear {
                performInitialSearch()
            }
            .sheet(isPresented: $showingFoodDetail) {
                if let food = selectedFood {
                    FoodDetailView(
                        food: food,
                        mealType: selectedMealType,
                        onAddFood: { record in
                            nutritionManager.addFoodRecord(record)
                            dismiss()
                        }
                    )
                }
            }
        }
    }
    
    private func performInitialSearch() {
        searchResults = foodRepository.searchFoods(query: "")
    }
    
    private func performSearch() {
        isLoading = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            var results = foodRepository.searchFoods(query: searchText)
            
            if let category = selectedCategory {
                results = results.filter { $0.category == category }
            }
            
            searchResults = results
            isLoading = false
        }
    }
}

struct SearchBar: View {
    @Binding var searchText: String
    let onSearchChanged: () -> Void
    
    var body: some View {
        HStack {
            Image(systemName: "magnifyingglass")
                .foregroundColor(.secondary)
            
            TextField("搜索食物名称...", text: $searchText)
                .textFieldStyle(PlainTextFieldStyle())
                .onChange(of: searchText) { _ in
                    onSearchChanged()
                }
            
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                    onSearchChanged()
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(.secondary)
                }
            }
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.systemGray6))
        .cornerRadius(10)
        .padding()
    }
}

struct CategoryFilterView: View {
    @Binding var selectedCategory: Food.FoodCategory?
    let onCategoryChanged: () -> Void
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                // 全部分类按钮
                CategoryButton(
                    title: "全部",
                    isSelected: selectedCategory == nil
                ) {
                    selectedCategory = nil
                    onCategoryChanged()
                }
                
                // 各个分类按钮
                ForEach(Food.FoodCategory.allCases, id: \.self) { category in
                    CategoryButton(
                        title: category.rawValue,
                        isSelected: selectedCategory == category
                    ) {
                        selectedCategory = category
                        onCategoryChanged()
                    }
                }
            }
            .padding(.horizontal)
        }
        .padding(.bottom, 8)
    }
}

struct CategoryButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.subheadline)
                .fontWeight(.medium)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    isSelected ? Color.green : Color(.systemGray5)
                )
                .foregroundColor(
                    isSelected ? .white : .primary
                )
                .cornerRadius(20)
        }
    }
}

struct FoodSearchResultsList: View {
    let foods: [Food]
    let onFoodSelected: (Food) -> Void
    
    var body: some View {
        List(foods) { food in
            FoodSearchResultRow(food: food) {
                onFoodSelected(food)
            }
        }
        .listStyle(PlainListStyle())
    }
}

struct FoodSearchResultRow: View {
    let food: Food
    let onTap: () -> Void
    
    var body: some View {
        Button(action: onTap) {
            HStack(spacing: 12) {
                // 食物图标
                Image(systemName: foodCategoryIcon(food.category))
                    .font(.title2)
                    .foregroundColor(foodCategoryColor(food.category))
                    .frame(width: 40, height: 40)
                    .background(foodCategoryColor(food.category).opacity(0.1))
                    .cornerRadius(8)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(food.name)
                        .font(.subheadline)
                        .fontWeight(.medium)
                        .foregroundColor(.primary)
                    
                    HStack {
                        if let brand = food.brand {
                            Text(brand)
                                .font(.caption)
                                .foregroundColor(.secondary)
                        }
                        
                        Text(food.category.rawValue)
                            .font(.caption)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(foodCategoryColor(food.category).opacity(0.2))
                            .foregroundColor(foodCategoryColor(food.category))
                            .cornerRadius(4)
                    }
                    
                    Text("每100g: \(Int(food.nutritionFacts.energyKcal))kcal")
                        .font(.caption)
                        .foregroundColor(.orange)
                }
                
                Spacer()
                
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            .padding(.vertical, 4)
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    private func foodCategoryIcon(_ category: Food.FoodCategory) -> String {
        switch category {
        case .grains: return "leaf.fill"
        case .vegetables: return "carrot.fill"
        case .meat: return "fork.knife"
        case .seafood: return "fish.fill"
        case .eggs: return "oval.fill"
        case .dairy: return "drop.fill"
        case .nuts: return "circle.fill"
        case .oils: return "drop.triangle.fill"
        case .beverages: return "cup.and.saucer.fill"
        case .snacks: return "gift.fill"
        case .other: return "questionmark.circle.fill"
        }
    }
    
    private func foodCategoryColor(_ category: Food.FoodCategory) -> Color {
        switch category {
        case .grains: return .brown
        case .vegetables: return .green
        case .meat: return .red
        case .seafood: return .blue
        case .eggs: return .yellow
        case .dairy: return .cyan
        case .nuts: return .orange
        case .oils: return .purple
        case .beverages: return .indigo
        case .snacks: return .pink
        case .other: return .gray
        }
    }
}

struct LoadingView: View {
    var body: some View {
        VStack(spacing: 16) {
            ProgressView()
                .scaleEffect(1.2)
            
            Text("搜索中...")
                .font(.subheadline)
                .foregroundColor(.secondary)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct EmptySearchResultsView: View {
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 50))
                .foregroundColor(.secondary)
            
            Text("未找到相关食物")
                .font(.headline)
                .fontWeight(.medium)
            
            Text("请尝试其他关键词或选择不同的分类")
                .font(.subheadline)
                .foregroundColor(.secondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

#Preview {
    FoodSearchView(selectedMealType: .lunch)
        .environmentObject(NutritionManager())
}
