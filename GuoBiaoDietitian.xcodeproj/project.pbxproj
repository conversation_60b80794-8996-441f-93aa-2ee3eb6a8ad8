// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A000001 /* GuoBiaoDietitianApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000000 /* GuoBiaoDietitianApp.swift */; };
		1A000003 /* ContentView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000002 /* ContentView.swift */; };
		1A000005 /* NutritionModels.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000004 /* NutritionModels.swift */; };
		1A000007 /* ChineseDietaryStandards.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000006 /* ChineseDietaryStandards.swift */; };
		1A000009 /* NutritionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000008 /* NutritionManager.swift */; };
		1A00000B /* HealthKitManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00000A /* HealthKitManager.swift */; };
		1A00000D /* FoodRepository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00000C /* FoodRepository.swift */; };
		1A00000F /* NutritionOverviewView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00000E /* NutritionOverviewView.swift */; };
		1A000011 /* FoodRecordView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000010 /* FoodRecordView.swift */; };
		1A000013 /* TrendAnalysisView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000012 /* TrendAnalysisView.swift */; };
		1A000015 /* AIAssistantView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000014 /* AIAssistantView.swift */; };
		1A000017 /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000016 /* SettingsView.swift */; };
		1A000019 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A000018 /* Assets.xcassets */; };
		1A00001C /* Preview Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A00001B /* Preview Assets.xcassets */; };
		1A000035 /* FoodSearchView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00001E /* FoodSearchView.swift */; };
		1A000036 /* FoodDetailView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A00001F /* FoodDetailView.swift */; };
		1A000037 /* NutritionDetailsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000020 /* NutritionDetailsView.swift */; };
		1A000038 /* BarcodeScannerManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000021 /* BarcodeScannerManager.swift */; };
		1A000039 /* FoodRecognitionManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000022 /* FoodRecognitionManager.swift */; };
		1A00003A /* Date+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000023 /* Date+Extensions.swift */; };
		1A00003B /* NutritionCalculator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000024 /* NutritionCalculator.swift */; };
		1A00003C /* SampleData.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A000025 /* SampleData.swift */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A000000 /* GuoBiaoDietitianApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GuoBiaoDietitianApp.swift; sourceTree = "<group>"; };
		1A000002 /* ContentView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ContentView.swift; sourceTree = "<group>"; };
		1A000004 /* NutritionModels.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NutritionModels.swift; sourceTree = "<group>"; };
		1A000006 /* ChineseDietaryStandards.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChineseDietaryStandards.swift; sourceTree = "<group>"; };
		1A000008 /* NutritionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NutritionManager.swift; sourceTree = "<group>"; };
		1A00000A /* HealthKitManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HealthKitManager.swift; sourceTree = "<group>"; };
		1A00000C /* FoodRepository.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoodRepository.swift; sourceTree = "<group>"; };
		1A00000E /* NutritionOverviewView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NutritionOverviewView.swift; sourceTree = "<group>"; };
		1A000010 /* FoodRecordView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoodRecordView.swift; sourceTree = "<group>"; };
		1A000012 /* TrendAnalysisView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrendAnalysisView.swift; sourceTree = "<group>"; };
		1A000014 /* AIAssistantView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AIAssistantView.swift; sourceTree = "<group>"; };
		1A000016 /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		1A000018 /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A00001B /* Preview Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = "Preview Assets.xcassets"; sourceTree = "<group>"; };
		1A00001D /* GuoBiaoDietitian.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = GuoBiaoDietitian.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A00001E /* FoodSearchView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoodSearchView.swift; sourceTree = "<group>"; };
		1A00001F /* FoodDetailView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoodDetailView.swift; sourceTree = "<group>"; };
		1A000020 /* NutritionDetailsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NutritionDetailsView.swift; sourceTree = "<group>"; };
		1A000021 /* BarcodeScannerManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BarcodeScannerManager.swift; sourceTree = "<group>"; };
		1A000022 /* FoodRecognitionManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FoodRecognitionManager.swift; sourceTree = "<group>"; };
		1A000023 /* Date+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Date+Extensions.swift"; sourceTree = "<group>"; };
		1A000024 /* NutritionCalculator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NutritionCalculator.swift; sourceTree = "<group>"; };
		1A000025 /* SampleData.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SampleData.swift; sourceTree = "<group>"; };
		1A000026 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A000027 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A000028 /* GuoBiaoDietitian */ = {
			isa = PBXGroup;
			children = (
				1A000029 /* App */,
				1A00002A /* Models */,
				1A00002B /* Views */,
				1A00002C /* ViewModels */,
				1A00002D /* Services */,
				1A00002E /* Extensions */,
				1A00002F /* Utils */,
				1A000030 /* Resources */,
				1A000031 /* Preview Content */,
				1A000026 /* Info.plist */,
			);
			path = GuoBiaoDietitian;
			sourceTree = "<group>";
		};
		1A000029 /* App */ = {
			isa = PBXGroup;
			children = (
				1A000000 /* GuoBiaoDietitianApp.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		1A00002A /* Models */ = {
			isa = PBXGroup;
			children = (
				1A000004 /* NutritionModels.swift */,
				1A000006 /* ChineseDietaryStandards.swift */,
			);
			path = Models;
			sourceTree = "<group>";
		};
		1A00002B /* Views */ = {
			isa = PBXGroup;
			children = (
				1A000002 /* ContentView.swift */,
				1A00000E /* NutritionOverviewView.swift */,
				1A000010 /* FoodRecordView.swift */,
				1A00001E /* FoodSearchView.swift */,
				1A00001F /* FoodDetailView.swift */,
				1A000020 /* NutritionDetailsView.swift */,
				1A000012 /* TrendAnalysisView.swift */,
				1A000014 /* AIAssistantView.swift */,
				1A000016 /* SettingsView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1A00002C /* ViewModels */ = {
			isa = PBXGroup;
			children = (
				1A000008 /* NutritionManager.swift */,
			);
			path = ViewModels;
			sourceTree = "<group>";
		};
		1A00002D /* Services */ = {
			isa = PBXGroup;
			children = (
				1A00000A /* HealthKitManager.swift */,
				1A00000C /* FoodRepository.swift */,
				1A000021 /* BarcodeScannerManager.swift */,
				1A000022 /* FoodRecognitionManager.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		1A00002E /* Extensions */ = {
			isa = PBXGroup;
			children = (
				1A000023 /* Date+Extensions.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		1A00002F /* Utils */ = {
			isa = PBXGroup;
			children = (
				1A000024 /* NutritionCalculator.swift */,
			);
			path = Utils;
			sourceTree = "<group>";
		};
		1A000030 /* Resources */ = {
			isa = PBXGroup;
			children = (
				1A000018 /* Assets.xcassets */,
				1A000025 /* SampleData.swift */,
			);
			path = Resources;
			sourceTree = "<group>";
		};
		1A000031 /* Preview Content */ = {
			isa = PBXGroup;
			children = (
				1A00001B /* Preview Assets.xcassets */,
			);
			path = "Preview Content";
			sourceTree = "<group>";
		};
		1A000032 /* Products */ = {
			isa = PBXGroup;
			children = (
				1A00001D /* GuoBiaoDietitian.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A000033 = {
			isa = PBXGroup;
			children = (
				1A000028 /* GuoBiaoDietitian */,
				1A000032 /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A000034 /* GuoBiaoDietitian */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A00003D /* Build configuration list for PBXNativeTarget "GuoBiaoDietitian" */;
			buildPhases = (
				1A00003E /* Sources */,
				1A000027 /* Frameworks */,
				1A00003F /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = GuoBiaoDietitian;
			productName = GuoBiaoDietitian;
			productReference = 1A00001D /* GuoBiaoDietitian.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A000040 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1500;
				LastUpgradeCheck = 1640;
				TargetAttributes = {
					1A000034 = {
						CreatedOnToolsVersion = 15.0;
					};
				};
			};
			buildConfigurationList = 1A000041 /* Build configuration list for PBXProject "GuoBiaoDietitian" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				"zh-Hans",
				Base,
			);
			mainGroup = 1A000033;
			productRefGroup = 1A000032 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A000034 /* GuoBiaoDietitian */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A00003F /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				1A00001C /* Preview Assets.xcassets in Resources */,
				1A000019 /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A00003E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				1A000003 /* ContentView.swift in Sources */,
				1A000001 /* GuoBiaoDietitianApp.swift in Sources */,
				1A000005 /* NutritionModels.swift in Sources */,
				1A000007 /* ChineseDietaryStandards.swift in Sources */,
				1A000009 /* NutritionManager.swift in Sources */,
				1A00000B /* HealthKitManager.swift in Sources */,
				1A00000D /* FoodRepository.swift in Sources */,
				1A00000F /* NutritionOverviewView.swift in Sources */,
				1A000011 /* FoodRecordView.swift in Sources */,
				1A000035 /* FoodSearchView.swift in Sources */,
				1A000036 /* FoodDetailView.swift in Sources */,
				1A000037 /* NutritionDetailsView.swift in Sources */,
				1A000013 /* TrendAnalysisView.swift in Sources */,
				1A000015 /* AIAssistantView.swift in Sources */,
				1A000017 /* SettingsView.swift in Sources */,
				1A000038 /* BarcodeScannerManager.swift in Sources */,
				1A000039 /* FoodRecognitionManager.swift in Sources */,
				1A00003A /* Date+Extensions.swift in Sources */,
				1A00003B /* NutritionCalculator.swift in Sources */,
				1A00003C /* SampleData.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A000042 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = J8QPZ64492;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A000043 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = J8QPZ64492;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_OPTIMIZATION_LEVEL = "-O";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1A000044 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"GuoBiaoDietitian/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GuoBiaoDietitian/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "国标营养师";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_NSCameraUsageDescription = "需要使用相机拍摄食物照片，以便AI识别并分析营养成分";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "需要访问健康数据以同步体重、步数等信息，帮助提供更准确的营养建议";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "需要写入营养数据到健康应用，以便统一管理您的健康信息";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问照片库以选择食物照片进行营养分析";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = "armv7 healthkit";
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UIUserInterfaceStyle = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.borealbit.guobiaodietitian.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1A000045 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"GuoBiaoDietitian/Preview Content\"";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = GuoBiaoDietitian/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "国标营养师";
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.healthcare-fitness";
				INFOPLIST_KEY_NSCameraUsageDescription = "需要使用相机拍摄食物照片，以便AI识别并分析营养成分";
				INFOPLIST_KEY_NSHealthShareUsageDescription = "需要访问健康数据以同步体重、步数等信息，帮助提供更准确的营养建议";
				INFOPLIST_KEY_NSHealthUpdateUsageDescription = "需要写入营养数据到健康应用，以便统一管理您的健康信息";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "需要访问照片库以选择食物照片进行营养分析";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UIRequiredDeviceCapabilities = "armv7 healthkit";
				INFOPLIST_KEY_UIStatusBarHidden = NO;
				INFOPLIST_KEY_UIStatusBarStyle = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UIUserInterfaceStyle = Automatic;
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.borealbit.guobiaodietitian.app;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A00003D /* Build configuration list for PBXNativeTarget "GuoBiaoDietitian" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A000044 /* Debug */,
				1A000045 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A000041 /* Build configuration list for PBXProject "GuoBiaoDietitian" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A000042 /* Debug */,
				1A000043 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 1A000040 /* Project object */;
}
