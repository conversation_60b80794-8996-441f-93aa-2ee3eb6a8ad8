# GuoBiaoDietitian iOS App

基于中国国标的营养膳食管理 iOS 应用

## 项目概述

GuoBiaoDietitian 是一款严格遵循中国国家标准的营养膳食管理应用，主要特点：

- 🇨🇳 **权威国标**：基于 GB 28050-2011 营养标签通则和《中国居民膳食指南 2022》
- 🤖 **本地 AI**：使用 Apple Foundation Models 2025 进行食物识别和营养分析
- 🔒 **隐私优先**：所有数据处理完全在设备本地完成
- 📱 **原生体验**：SwiftUI + Core ML + HealthKit 深度集成

## 核心功能

### 1. 食物记录
- 📸 拍照识别（Vision + Core ML）
- 📊 条码扫描（GB 28050 营养标签）
- 🎤 语音/文本快速输入

### 2. 国标解析引擎
- 按《中国居民膳食指南 2022》推荐量计算三餐配比
- 结合《成人肥胖食养指南 2024》给出能量"超/缺额"

### 3. 实时评估
- 卡路里 & 三大宏量营养素"红黄绿"提示
- 盐、糖、脂肪摄入超标预警
- 每日、周、月趋势图

### 4. AI 解释器
- 自然语言回答："为什么我今天脂肪超标？"
- 给出具体国标条文片段与浅白解释

### 5. 个性化建议
- 根据 BMI、活动量动态调节阈值
- 生成可执行的食谱或替换方案

## 技术架构

| 层级 | 技术要点 | 关键依赖 |
|------|----------|----------|
| **UI 层** | SwiftUI + MVVM + Combine | iOS 17+ |
| **AI 层** | Apple Foundation Models 2025 (3-7B 参数) | Core ML 5 / MLX-LM |
| **数据层** | HealthKit + AppStorage + CloudKit | Xcode 26 |

## 项目结构

```
GuoBiaoDietitian/
├── App/                    # 应用入口
├── Models/                 # 数据模型
├── Views/                  # SwiftUI 视图
├── ViewModels/            # MVVM 视图模型
├── Services/              # 业务服务层
├── Resources/             # 资源文件
├── Extensions/            # 扩展
└── Utils/                 # 工具类
```

## 开发路线图

| Sprint | 目标 | 关键交付 |
|--------|------|----------|
| **0–2 周** | ✅ 项目初始化、CI/CD、基础数据模型 | 建库 `GuoBiaoDietitian`、App 结构骨架 |
| **3–6 周** | 🔍 食物识别 MVP + 手动录入 | Vision 识别 + GB 28050 数据表 |
| **7–10 周** | 🧠 本地 LLM 集成 + 国标 QA | LoRA 微调、国标条文向量检索 |
| **11–14 周** | 📈 趋势可视化 + Watch 支持 | HealthKit 同步、Swift Charts |
| **15–18 周** | 🔐 Beta 测试 & 隐私合规 | TestFlight、隐私协议、App Store 审核准备 |

## 开始开发

1. 确保 Xcode 16+ 和 iOS 17+ 开发环境
2. 克隆项目并打开 `GuoBiaoDietitian.xcodeproj`
3. 运行项目查看基础界面

## 许可证

MIT License