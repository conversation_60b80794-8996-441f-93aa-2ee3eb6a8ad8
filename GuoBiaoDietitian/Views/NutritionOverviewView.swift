import SwiftUI

struct NutritionOverviewView: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    @EnvironmentObject var healthKitManager: HealthKitManager
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 20) {
                    // 今日营养概览卡片
                    TodayNutritionCard()
                    
                    // 宏量营养素圆环图
                    MacronutrientRingsView()
                    
                    // 营养状态指示器
                    NutritionStatusGrid()
                    
                    // 今日食物记录
                    TodayFoodRecordsSection()
                    
                    Spacer(minLength: 100) // 为底部 TabBar 留出空间
                }
                .padding()
            }
            .navigationTitle("营养概览")
            .refreshable {
                nutritionManager.loadTodayRecords()
                healthKitManager.loadHealthData()
            }
        }
    }
}

struct TodayNutritionCard: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Text("今日营养摄入")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Text(Date(), style: .date)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
            
            let summary = nutritionManager.todayNutritionSummary
            
            HStack(spacing: 20) {
                // 能量
                NutritionMetricView(
                    title: "能量",
                    current: Int(summary.totalNutrition.energyKcal),
                    target: Int(summary.recommendation?.energy ?? 2000),
                    unit: "kcal",
                    color: .orange
                )
                
                Divider()
                
                // 蛋白质
                NutritionMetricView(
                    title: "蛋白质",
                    current: Int(summary.totalNutrition.protein),
                    target: Int(summary.recommendation?.protein ?? 60),
                    unit: "g",
                    color: .blue
                )
                
                Divider()
                
                // 脂肪
                NutritionMetricView(
                    title: "脂肪",
                    current: Int(summary.totalNutrition.fat),
                    target: Int(summary.recommendation?.fat ?? 60),
                    unit: "g",
                    color: .yellow
                )
                
                Divider()
                
                // 碳水化合物
                NutritionMetricView(
                    title: "碳水",
                    current: Int(summary.totalNutrition.carbohydrate),
                    target: Int(summary.recommendation?.carbohydrate ?? 300),
                    unit: "g",
                    color: .green
                )
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct NutritionMetricView: View {
    let title: String
    let current: Int
    let target: Int
    let unit: String
    let color: Color
    
    private var progress: Double {
        guard target > 0 else { return 0 }
        return min(Double(current) / Double(target), 1.0)
    }
    
    var body: some View {
        VStack(spacing: 4) {
            Text(title)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("\(current)")
                .font(.title2)
                .fontWeight(.bold)
                .foregroundColor(color)
            
            Text("/ \(target) \(unit)")
                .font(.caption2)
                .foregroundColor(.secondary)
            
            // 进度条
            ProgressView(value: progress)
                .progressViewStyle(LinearProgressViewStyle(tint: color))
                .scaleEffect(x: 1, y: 0.5)
        }
    }
}

struct MacronutrientRingsView: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("宏量营养素分布")
                .font(.headline)
                .fontWeight(.semibold)
            
            if let balance = nutritionManager.getMacronutrientBalance() {
                HStack(spacing: 30) {
                    // 营养圆环
                    ZStack {
                        // 背景圆环
                        Circle()
                            .stroke(Color.gray.opacity(0.2), lineWidth: 20)
                            .frame(width: 120, height: 120)
                        
                        // 蛋白质圆环 (内层)
                        Circle()
                            .trim(from: 0, to: balance.proteinPercentage / 100)
                            .stroke(Color.blue, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                            .frame(width: 80, height: 80)
                            .rotationEffect(.degrees(-90))
                        
                        // 脂肪圆环 (中层)
                        Circle()
                            .trim(from: 0, to: balance.fatPercentage / 100)
                            .stroke(Color.yellow, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                            .frame(width: 100, height: 100)
                            .rotationEffect(.degrees(-90))
                        
                        // 碳水化合物圆环 (外层)
                        Circle()
                            .trim(from: 0, to: balance.carbohydratePercentage / 100)
                            .stroke(Color.green, style: StrokeStyle(lineWidth: 8, lineCap: .round))
                            .frame(width: 120, height: 120)
                            .rotationEffect(.degrees(-90))
                        
                        // 中心文字
                        VStack {
                            Text(balance.isBalanced ? "均衡" : "失衡")
                                .font(.caption)
                                .fontWeight(.semibold)
                                .foregroundColor(balance.isBalanced ? .green : .orange)
                        }
                    }
                    
                    // 图例
                    VStack(alignment: .leading, spacing: 8) {
                        MacronutrientLegendItem(
                            color: .blue,
                            name: "蛋白质",
                            percentage: balance.proteinPercentage,
                            target: "10-15%"
                        )
                        
                        MacronutrientLegendItem(
                            color: .yellow,
                            name: "脂肪",
                            percentage: balance.fatPercentage,
                            target: "20-30%"
                        )
                        
                        MacronutrientLegendItem(
                            color: .green,
                            name: "碳水化合物",
                            percentage: balance.carbohydratePercentage,
                            target: "50-65%"
                        )
                    }
                    
                    Spacer()
                }
            } else {
                Text("暂无数据")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding(.vertical, 40)
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct MacronutrientLegendItem: View {
    let color: Color
    let name: String
    let percentage: Double
    let target: String
    
    var body: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(color)
                .frame(width: 12, height: 12)
            
            VStack(alignment: .leading, spacing: 2) {
                Text(name)
                    .font(.caption)
                    .fontWeight(.medium)
                
                HStack {
                    Text("\(Int(percentage))%")
                        .font(.caption2)
                        .fontWeight(.semibold)
                    
                    Text("(\(target))")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                }
            }
        }
    }
}

struct NutritionStatusGrid: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("营养状态评估")
                .font(.headline)
                .fontWeight(.semibold)
            
            let summary = nutritionManager.todayNutritionSummary
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                NutritionStatusItem(
                    name: "钠",
                    amount: summary.totalNutrition.sodium,
                    unit: "mg",
                    status: nutritionManager.getNutritionStatus(for: "sodium", amount: summary.totalNutrition.sodium)
                )
                
                if let fiber = summary.totalNutrition.dietaryFiber {
                    NutritionStatusItem(
                        name: "膳食纤维",
                        amount: fiber,
                        unit: "g",
                        status: nutritionManager.getNutritionStatus(for: "dietaryFiber", amount: fiber)
                    )
                }
                
                if let calcium = summary.totalNutrition.calcium {
                    NutritionStatusItem(
                        name: "钙",
                        amount: calcium,
                        unit: "mg",
                        status: nutritionManager.getNutritionStatus(for: "calcium", amount: calcium)
                    )
                }
                
                if let iron = summary.totalNutrition.iron {
                    NutritionStatusItem(
                        name: "铁",
                        amount: iron,
                        unit: "mg",
                        status: nutritionManager.getNutritionStatus(for: "iron", amount: iron)
                    )
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct NutritionStatusItem: View {
    let name: String
    let amount: Double
    let unit: String
    let status: NutritionStatus
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(name)
                    .font(.caption)
                    .foregroundColor(.secondary)
                
                Text("\(Int(amount)) \(unit)")
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(status.rawValue)
                    .font(.caption2)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(status.color.opacity(0.2))
                    .foregroundColor(status.color)
                    .cornerRadius(4)
            }
            
            Spacer()
            
            Circle()
                .fill(status.color)
                .frame(width: 8, height: 8)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 8)
        .background(Color(.secondarySystemBackground))
        .cornerRadius(8)
    }
}

struct TodayFoodRecordsSection: View {
    @EnvironmentObject var nutritionManager: NutritionManager
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Text("今日食物记录")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                NavigationLink(destination: FoodRecordView()) {
                    Image(systemName: "plus.circle.fill")
                        .foregroundColor(.green)
                        .font(.title2)
                }
            }
            
            if nutritionManager.todayRecords.isEmpty {
                VStack(spacing: 12) {
                    Image(systemName: "fork.knife.circle")
                        .font(.system(size: 40))
                        .foregroundColor(.secondary)
                    
                    Text("今天还没有记录任何食物")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                    
                    Text("点击右上角 + 开始记录")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 30)
            } else {
                ForEach(nutritionManager.todayRecords.prefix(3)) { record in
                    FoodRecordRow(record: record)
                }
                
                if nutritionManager.todayRecords.count > 3 {
                    NavigationLink(destination: FoodRecordListView()) {
                        Text("查看全部 \(nutritionManager.todayRecords.count) 条记录")
                            .font(.caption)
                            .foregroundColor(.blue)
                            .frame(maxWidth: .infinity, alignment: .center)
                            .padding(.vertical, 8)
                    }
                }
            }
        }
        .padding()
        .background(Color(.systemBackground))
        .cornerRadius(12)
        .shadow(color: .black.opacity(0.1), radius: 5, x: 0, y: 2)
    }
}

struct FoodRecordRow: View {
    let record: FoodRecord
    
    var body: some View {
        HStack {
            VStack(alignment: .leading, spacing: 4) {
                Text(record.food.name)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                HStack {
                    Text(record.mealType.rawValue)
                        .font(.caption)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.blue.opacity(0.2))
                        .foregroundColor(.blue)
                        .cornerRadius(4)
                    
                    Text("\(Int(record.amount))g")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    
                    Spacer()
                    
                    Text(record.timestamp, style: .time)
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            
            Spacer()
            
            Text("\(Int(record.actualNutrition.energyKcal)) kcal")
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(.orange)
        }
        .padding(.vertical, 8)
    }
}

#Preview {
    NutritionOverviewView()
        .environmentObject(NutritionManager())
        .environmentObject(HealthKitManager())
}
